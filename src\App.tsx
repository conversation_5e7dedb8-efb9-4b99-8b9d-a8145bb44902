import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from './components/Layout';
import { Dashboard } from './pages/Dashboard';
import { Agents } from './pages/Agents';
import { AgentConfig } from './pages/AgentConfig';
import { TestAiAgent } from './pages/TestAiAgent';
import { KnowledgeBase } from './pages/KnowledgeBase';
import { CallHistory } from './pages/CallHistory';
import { PhoneNumbers } from './pages/PhoneNumbers';
import { Tools } from './pages/Tools';
import { Settings } from './pages/Settings';
import { VapiDebug } from './components/VapiDebug';
import { ModalProvider } from './context/ModalContext';

function App() {
  return (
    <ModalProvider>
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path="/" element={<Navigate to="/app/dashboard" replace />} />
            <Route path="/app/dashboard" element={<Dashboard />} />
            <Route path="/app/agents" element={<Agents />} />
            <Route path="/app/agents/:agentId" element={<AgentConfig />} />
            <Route path="/app/talk-to/:agentId" element={<TestAiAgent />} />
            <Route path="/app/knowledge-base" element={<KnowledgeBase />} />
            <Route path="/app/call-history" element={<CallHistory />} />
            <Route path="/app/phone-numbers" element={<PhoneNumbers />} />
            <Route path="/app/tools" element={<Tools />} />
            <Route path="/app/settings" element={<Settings />} />
            <Route path="/app/debug" element={<div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6"><VapiDebug /></div>} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </ModalProvider>
  );
}

export default App;