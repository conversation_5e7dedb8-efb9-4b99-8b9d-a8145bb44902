import React from 'react';
import { Search, ChevronRight } from 'lucide-react';
import { CallDetails } from '../components/CallDetails';

interface CallRecord {
  id: string;
  date: string;
  time: string;
  agent: string;
  duration: string;
  messages: number;
  status: 'successful' | 'error' | 'unknown';
  summary: string;
  metadata: {
    connectionDuration: string;
    cost: number;
    llmPricePreview: number;
  };
}

const callRecords: CallRecord[] = [
  {
    id: 'HFuKR97LgwrosBmz6oMD',
    date: 'May 9, 2025',
    time: '1:15 PM',
    agent: 'John - Atlas Construction',
    duration: '2:16',
    messages: 16,
    status: 'successful',
    summary: '<PERSON> from Atlas Roofing contacted the user to schedule a roofing inspection due to recent hail damage. The user confirmed it was for their home and wanted an inspection of their tile roof, mentioning potential damage and interest in insurance assistance. An inspection was scheduled for Tuesday at 2 PM, and the user will point out specific areas of concern during the visit. Atlas Roofing will send a confirmation message.',
    metadata: {
      connectionDuration: '2:43',
      cost: 546,
      llmPricePreview: 0.0142
    }
  },
  {
    id: 'KLmNP97QrstuVwx8yZAB',
    date: 'May 9, 2025',
    time: '1:45 PM',
    agent: 'John - Atlas Construction',
    duration: '3:37',
    messages: 10,
    status: 'successful',
    summary: 'Follow-up call regarding scheduled roof inspection. Confirmed appointment details and discussed specific areas of concern.',
    metadata: {
      connectionDuration: '3:45',
      cost: 612,
      llmPricePreview: 0.0156
    }
  }
];

export const CallHistory: React.FC = () => {
  const [selectedCall, setSelectedCall] = React.useState<CallRecord | null>(null);

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold mb-1">Call History</h1>
          <p className="text-gray-400">View and analyze your conversation history</p>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-2 mb-6">
          <button className="px-3 py-1 bg-[#1A1A1A] rounded-full text-sm text-gray-400 hover:bg-[#252525] transition-colors">
            Date filter
          </button>
          <button className="px-3 py-1 bg-[#1A1A1A] rounded-full text-sm text-gray-400 hover:bg-[#252525] transition-colors">
            Evaluation
          </button>
          <button className="px-3 py-1 bg-[#1A1A1A] rounded-full text-sm text-gray-400 hover:bg-[#252525] transition-colors">
            Agent
          </button>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search calls..."
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {/* Table */}
        <div className="bg-[#1A1A1A] rounded-lg border border-gray-800">
          <div className="grid grid-cols-[180px,1fr,120px,100px,150px] px-4 py-3 border-b border-gray-800">
            <div className="text-sm text-gray-400">Date</div>
            <div className="text-sm text-gray-400">Agent</div>
            <div className="text-sm text-gray-400">Duration</div>
            <div className="text-sm text-gray-400">Messages</div>
            <div className="text-sm text-gray-400">Evaluation result</div>
          </div>
          
          {callRecords.map((record, index) => (
            <div
              key={index}
              className="grid grid-cols-[180px,1fr,120px,100px,150px] px-4 py-3 hover:bg-[#252525] transition-colors cursor-pointer group"
              onClick={() => setSelectedCall(record)}
            >
              <div className="text-sm text-gray-400">{record.date}, {record.time}</div>
              <div className="text-sm font-medium">{record.agent}</div>
              <div className="text-sm text-gray-400">{record.duration}</div>
              <div className="text-sm text-gray-400">{record.messages}</div>
              <div className="flex items-center">
                <span className={`px-2 py-0.5 rounded-full text-xs ${
                  record.status === 'successful' ? 'bg-green-500/20 text-green-500' :
                  record.status === 'error' ? 'bg-red-500/20 text-red-500' :
                  'bg-gray-500/20 text-gray-400'
                }`}>
                  {record.status === 'successful' ? 'Successful' :
                   record.status === 'error' ? 'Error' : 'Unknown'}
                </span>
              </div>
              <div className="absolute right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <ChevronRight size={16} className="text-gray-400" />
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {selectedCall && (
        <CallDetails
          call={selectedCall}
          onClose={() => setSelectedCall(null)}
        />
      )}
    </div>
  );
};