import { StateGraph, END, START } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, AIMessage, SystemMessage } from "@langchain/core/messages";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";

// Define the agent state
interface AgentState {
  messages: Array<HumanMessage | AIMessage | SystemMessage>;
  next?: string;
}

// MCP Tool for code modifications
const mcpCodeTool = new DynamicStructuredTool({
  name: "modify_code",
  description: "Modify code files in the project. Can create, edit, or update files based on user requests.",
  schema: z.object({
    action: z.enum(["create", "edit", "update", "view"]).describe("The action to perform"),
    filePath: z.string().describe("The file path relative to project root"),
    content: z.string().optional().describe("The content to write or changes to make"),
    description: z.string().describe("Description of what changes are being made"),
  }),
  func: async ({ action, filePath, content, description }) => {
    try {
      console.log(`🔧 MCP Code Tool - ${action} on ${filePath}: ${description}`);

      // Simulate real file operations with detailed responses
      switch (action) {
        case "create":
          if (!content) {
            return `❌ Error: Content is required for creating files`;
          }
          return `✅ Created file: ${filePath}
📝 Description: ${description}
📄 Content preview: ${content.substring(0, 200)}${content.length > 200 ? '...' : ''}
🎯 Next steps: File is ready for use in the application`;

        case "edit":
          return `✅ Edited file: ${filePath}
📝 Changes made: ${description}
🔄 Status: File has been successfully modified
🎯 Next steps: Changes are now active in the application`;

        case "update":
          return `✅ Updated file: ${filePath}
📝 Updates applied: ${description}
🔄 Status: File has been successfully updated
🎯 Next steps: Updates are now live in the application`;

        case "view":
          return `👀 Viewing file: ${filePath}
📁 File exists and is accessible
📝 Description: ${description}
🎯 Next steps: Ready for modifications if needed`;

        default:
          return `❌ Unknown action: ${action}`;
      }
    } catch (error) {
      return `❌ Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
    }
  },
});

// Vapi Integration Tool
const vapiIntegrationTool = new DynamicStructuredTool({
  name: "vapi_integration",
  description: "Integrate with Vapi services - create tools, assistants, manage knowledge bases, etc.",
  schema: z.object({
    service: z.enum(["tools", "assistants", "knowledge-base", "calls"]).describe("The Vapi service to interact with"),
    action: z.enum(["create", "update", "delete", "list", "get"]).describe("The action to perform"),
    data: z.record(z.any()).optional().describe("Data for the action"),
    description: z.string().describe("Description of the Vapi operation"),
  }),
  func: async ({ service, action, data, description }) => {
    try {
      console.log(`📞 Vapi Integration - ${action} on ${service}: ${description}`);

      // Simulate real Vapi API operations with detailed responses
      switch (service) {
        case "tools":
          switch (action) {
            case "create":
              return `✅ Created Vapi Tool
🔧 Tool Type: ${data?.type || 'Custom Function'}
📝 Description: ${description}
🎯 Tool ID: tool_${Date.now()}
📋 Configuration: ${JSON.stringify(data, null, 2)}
🚀 Status: Tool is now available for use in assistants`;

            case "list":
              return `📋 Listed Vapi Tools
🔧 Available tool types: Custom Functions, Google Calendar, Slack, GoHighLevel, MCP
📊 Total tools: 12 active tools
🎯 Recent tools: Google Calendar integration, Slack notifications, Custom webhooks`;

            default:
              return `✅ ${action} tool operation completed: ${description}`;
          }

        case "assistants":
          switch (action) {
            case "create":
              return `✅ Created Vapi Assistant
🤖 Assistant Name: ${data?.name || 'New Assistant'}
📝 Description: ${description}
🎯 Assistant ID: asst_${Date.now()}
🧠 Model: ${data?.model || 'gpt-4o'}
🔧 Tools: ${data?.tools?.length || 0} tools attached
🚀 Status: Assistant is ready for calls`;

            case "list":
              return `📋 Listed Vapi Assistants
🤖 Active assistants: 5 assistants
📊 Total calls: 1,247 calls this month
🎯 Top performing: Customer Support Bot, Sales Assistant`;

            default:
              return `✅ ${action} assistant operation completed: ${description}`;
          }

        case "knowledge-base":
          switch (action) {
            case "create":
              return `✅ Created Knowledge Base
📚 KB Name: ${data?.name || 'New Knowledge Base'}
📝 Description: ${description}
🎯 KB ID: kb_${Date.now()}
🔍 Search Type: ${data?.searchType || 'semantic'}
📄 Files: ${data?.files?.length || 0} files uploaded
🚀 Status: Knowledge base is ready for queries`;

            case "list":
              return `📋 Listed Knowledge Bases
📚 Active KBs: 8 knowledge bases
📄 Total files: 156 documents
🔍 Search types: Semantic, Fulltext, Hybrid`;

            default:
              return `✅ ${action} knowledge base operation completed: ${description}`;
          }

        case "calls":
          switch (action) {
            case "create":
              return `✅ Created Vapi Call
📞 Call Type: ${data?.type || 'Outbound'}
📝 Description: ${description}
🎯 Call ID: call_${Date.now()}
📱 Phone: ${data?.phoneNumber || '+1234567890'}
🤖 Assistant: ${data?.assistantId || 'Default Assistant'}
🚀 Status: Call initiated successfully`;

            case "list":
              return `📋 Listed Vapi Calls
📞 Recent calls: 25 calls today
📊 Success rate: 94.2%
⏱️ Avg duration: 3m 42s
🎯 Top use cases: Customer support, Lead qualification`;

            default:
              return `✅ ${action} call operation completed: ${description}`;
          }

        default:
          return `❌ Unknown service: ${service}`;
      }
    } catch (error) {
      return `❌ Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
    }
  },
});

// UI Component Tool
const uiComponentTool = new DynamicStructuredTool({
  name: "ui_component",
  description: "Create or modify UI components, pages, and styling in the React application.",
  schema: z.object({
    componentType: z.enum(["page", "component", "hook", "style"]).describe("Type of UI element"),
    name: z.string().describe("Name of the component or file"),
    changes: z.string().describe("Description of changes to make"),
    framework: z.enum(["react", "tailwind", "typescript"]).describe("Framework/technology being used"),
  }),
  func: async ({ componentType, name, changes, framework }) => {
    try {
      console.log(`🎨 UI Component Tool - ${componentType} ${name} using ${framework}: ${changes}`);

      // Simulate real UI component operations with detailed responses
      switch (componentType) {
        case "page":
          return `✅ Created/Modified Page: ${name}
🎨 Framework: ${framework}
📝 Changes: ${changes}
📁 Location: src/pages/${name}.tsx
🔧 Features: Responsive design, dark theme, TypeScript support
🎯 Status: Page is ready and integrated into routing`;

        case "component":
          return `✅ Created/Modified Component: ${name}
🎨 Framework: ${framework}
📝 Changes: ${changes}
📁 Location: src/components/${name}.tsx
🔧 Features: Reusable, accessible, styled with Tailwind
🎯 Status: Component is ready for use across the application`;

        case "hook":
          return `✅ Created/Modified Hook: ${name}
🎨 Framework: ${framework}
📝 Changes: ${changes}
📁 Location: src/hooks/${name}.ts
🔧 Features: TypeScript support, error handling, optimized performance
🎯 Status: Hook is ready for use in components`;

        case "style":
          return `✅ Updated Styling: ${name}
🎨 Framework: ${framework}
📝 Changes: ${changes}
🎨 Styling: Tailwind CSS classes, responsive design
🔧 Features: Dark theme compatible, mobile-first approach
🎯 Status: Styles are applied and active`;

        default:
          return `❌ Unknown component type: ${componentType}`;
      }
    } catch (error) {
      return `❌ Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
    }
  },
});

// Create the LangGraph agent
export class LangGraphMCPAgent {
  private model: ChatOpenAI;
  private tools: DynamicStructuredTool[];
  private graph: StateGraph<AgentState>;

  constructor(apiKey?: string) {
    this.model = new ChatOpenAI({
      modelName: "gpt-4o",
      temperature: 0.1,
      openAIApiKey: apiKey || process.env.OPENAI_API_KEY,
    });

    this.tools = [mcpCodeTool, vapiIntegrationTool, uiComponentTool];

    // Bind tools to the model
    const modelWithTools = this.model.bindTools(this.tools);

    // Create the state graph
    this.graph = new StateGraph<AgentState>({
      channels: {
        messages: {
          reducer: (x: any[], y: any[]) => x.concat(y),
          default: () => [],
        },
        next: {
          reducer: (x: string, y: string) => y,
          default: () => "",
        },
      },
    });

    // Define the agent node
    const agentNode = async (state: AgentState) => {
      const systemMessage = new SystemMessage(`You are a helpful AI assistant that can modify code, integrate with Vapi services, and update UI components through MCP (Model Context Protocol) tools.

You have access to these tools:
1. modify_code - Create, edit, or update code files
2. vapi_integration - Interact with Vapi services (tools, assistants, knowledge bases, calls)
3. ui_component - Create or modify React UI components and styling

When a user asks you to make changes:
1. Understand what they want to modify
2. Use the appropriate tool(s) to make the changes
3. Provide clear feedback about what was done
4. Ask for confirmation or further modifications if needed

Be conversational and helpful. Always explain what you're doing and why.`);

      const messages = [systemMessage, ...state.messages];
      const response = await modelWithTools.invoke(messages);

      return {
        messages: [response],
        next: response.tool_calls && response.tool_calls.length > 0 ? "tools" : END,
      };
    };

    // Create tool node
    const toolNode = new ToolNode(this.tools);

    // Add nodes to the graph
    this.graph.addNode("agent", agentNode);
    this.graph.addNode("tools", toolNode);

    // Add edges
    this.graph.addEdge(START, "agent");
    this.graph.addConditionalEdges("agent", (state: AgentState) => state.next || END);
    this.graph.addEdge("tools", "agent");

    // Compile the graph
    this.graph = this.graph.compile();
  }

  async chat(message: string): Promise<string> {
    try {
      const humanMessage = new HumanMessage(message);
      const initialState: AgentState = {
        messages: [humanMessage],
      };

      const result = await this.graph.invoke(initialState);
      const lastMessage = result.messages[result.messages.length - 1];

      if (lastMessage instanceof AIMessage) {
        return lastMessage.content as string;
      }

      return "I'm sorry, I couldn't process that request.";
    } catch (error) {
      console.error("Agent error:", error);
      return `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
    }
  }

  async streamChat(message: string): Promise<AsyncGenerator<string, void, unknown>> {
    const humanMessage = new HumanMessage(message);
    const initialState: AgentState = {
      messages: [humanMessage],
    };

    const stream = await this.graph.stream(initialState);

    async function* streamGenerator() {
      for await (const chunk of stream) {
        if (chunk.agent && chunk.agent.messages) {
          const lastMessage = chunk.agent.messages[chunk.agent.messages.length - 1];
          if (lastMessage instanceof AIMessage) {
            yield lastMessage.content as string;
          }
        }
      }
    }

    return streamGenerator();
  }
}

// Export a singleton instance
export const mcpAgent = new LangGraphMCPAgent();
