import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Globe2, Plus, FileText, PenTool as Tool, Key, ChevronDown, MoreVertical, Copy, AlertCircle, Save, ArrowLeft } from 'lucide-react';
import { Modal } from '../components/Modal';
import { ApiKeySetup } from '../components/ApiKeySetup';
import { useAssistant } from '../hooks/useAssistant';

interface ConfigSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  action?: React.ReactNode;
}

const ConfigSection: React.FC<ConfigSectionProps> = ({ title, description, children, action }) => (
  <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-6">
    <div className="flex items-center justify-between mb-4">
      <div>
        <h3 className="text-sm font-medium mb-1">{title}</h3>
        {description && <p className="text-sm text-gray-400">{description}</p>}
      </div>
      {action}
    </div>
    {children}
  </div>
);

export const AgentConfig: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = React.useState('agent');
  const [isKnowledgeBaseModalOpen, setIsKnowledgeBaseModalOpen] = React.useState(false);
  const [selectedDocument, setSelectedDocument] = React.useState<{
    name: string;
    content: string;
    id: string;
    lastUpdated: string;
    dependentAgents: string[];
  } | null>(null);
  const [hasApiKey, setHasApiKey] = React.useState(false);

  // Check for API key on mount
  React.useEffect(() => {
    const apiKey = import.meta.env.VITE_VAPI_API_KEY || localStorage.getItem('vapi_api_key');
    setHasApiKey(!!apiKey);
  }, []);

  const {
    assistant,
    loading,
    error,
    saving,
    updateAssistant,
    testAssistant,
    debouncedAutoSave,
    clearError,
  } = useAssistant();

  // Handle form field changes with auto-save
  const handleFieldChange = (field: string, value: any) => {
    if (assistant) {
      debouncedAutoSave(field as any, value);
    }
  };

  // Show API key setup if no key is found
  if (!hasApiKey) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white p-6">
        <ApiKeySetup onApiKeySet={() => setHasApiKey(true)} />
      </div>
    );
  }

  // Show loading state while fetching assistant
  if (loading && !assistant) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-4"></div>
          <p className="text-gray-400">Loading assistant configuration...</p>
        </div>
      </div>
    );
  }

  // Show error state if assistant not found
  if (!assistant && !loading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white">
        <div className="text-center max-w-md">
          <AlertCircle size={48} className="text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Assistant Not Found</h2>
          <p className="text-gray-400 mb-6">
            The assistant you're looking for doesn't exist in your Vapi account or has been deleted.
          </p>
          {error && (
            <div className="mb-6 p-3 bg-red-900/20 border border-red-800 rounded-lg">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}
          <div className="flex gap-3 justify-center">
            <button
              onClick={() => navigate('/app/agents')}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft size={16} />
              Back to Agents
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white">
      {/* Header with tabs and status */}
      <div className="border-b border-gray-800">
        <div className="flex items-center justify-between px-6 py-4">
          <h1 className="text-xl font-semibold">{assistant?.name || 'Assistant Configuration'}</h1>
          <div className="flex items-center gap-4">
            {saving && (
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Save size={16} className="animate-pulse" />
                Saving...
              </div>
            )}
            {error && (
              <div className="flex items-center gap-2 text-sm text-red-400">
                <AlertCircle size={16} />
                <span>{error}</span>
                <button onClick={clearError} className="text-red-400 hover:text-red-300">×</button>
              </div>
            )}
          </div>
        </div>

        <div className="flex">
          <TabButton
            label="Agent"
            active={activeTab === 'agent'}
            onClick={() => setActiveTab('agent')}
          />
          <TabButton
            label="Voice"
            active={activeTab === 'voice'}
            onClick={() => setActiveTab('voice')}
          />
          <TabButton
            label="Analysis"
            active={activeTab === 'analysis'}
            onClick={() => setActiveTab('analysis')}
          />
          <TabButton
            label="Security"
            active={activeTab === 'security'}
            onClick={() => setActiveTab('security')}
          />
          <TabButton
            label="Advanced"
            active={activeTab === 'advanced'}
            onClick={() => setActiveTab('advanced')}
          />
        </div>
      </div>

      {activeTab === 'agent' && (
        <div className="max-w-[640px] mx-auto py-8 space-y-6">
        {/* Language Settings */}
        <ConfigSection
          title="Agent Language"
          description="Choose the default language the agent will communicate in."
        >
          <button className="flex items-center justify-between w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700">
            <div className="flex items-center gap-2">
              <Globe2 size={18} className="text-gray-400" />
              <span>English</span>
            </div>
            <ChevronDown size={18} className="text-gray-400" />
          </button>
        </ConfigSection>

        {/* Additional Languages */}
        <ConfigSection
          title="Additional Languages"
          description="Specify additional languages which callers can choose from."
        >
          <button className="flex items-center w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700 text-gray-400">
            <Plus size={18} className="mr-2" />
            <span>Add additional languages</span>
          </button>
        </ConfigSection>

        {/* First Message */}
        <ConfigSection
          title="First message"
          description="The first message the agent will say. If empty, the agent will wait for the user to start the conversation."
        >
          <textarea
            className="w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700 min-h-[100px] text-sm"
            value={assistant?.firstMessage || ''}
            onChange={(e) => handleFieldChange('firstMessage', e.target.value)}
            placeholder="Enter the first message the agent will say..."
          />
          <button className="mt-2 text-sm text-gray-400 hover:text-white flex items-center gap-1">
            <Plus size={16} />
            Add Variable
          </button>
        </ConfigSection>

        {/* System Prompt */}
        <ConfigSection
          title="System prompt"
          description="The system prompt is used to determine the persona of the agent and the context of the conversation."
          action={<button className="text-sm text-white hover:underline">Learn more</button>}
        >
          <textarea
            className="w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700 min-h-[200px] text-sm"
            value={assistant?.model?.messages?.find(m => m.role === 'system')?.content || ''}
            onChange={(e) => {
              const systemMessage = e.target.value;
              const currentMessages = assistant?.model?.messages || [];
              const otherMessages = currentMessages.filter(m => m.role !== 'system');
              const newMessages = systemMessage
                ? [{ role: 'system' as const, content: systemMessage }, ...otherMessages]
                : otherMessages;

              handleFieldChange('model', {
                ...assistant?.model,
                messages: newMessages,
              });
            }}
            placeholder="Enter the system prompt that defines the agent's persona and behavior..."
          />
          <button className="mt-2 text-sm text-gray-400 hover:text-white flex items-center gap-1">
            <Plus size={16} />
            Add Variable
          </button>
        </ConfigSection>

        {/* LLM Selection */}
        <ConfigSection
          title="LLM"
          description="Select which provider and model to use for the LLM."
        >
          <select
            className="w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700 text-sm"
            value={`${assistant?.model?.provider || 'openai'}-${assistant?.model?.model || 'gpt-4'}`}
            onChange={(e) => {
              const [provider, model] = e.target.value.split('-');
              handleFieldChange('model', {
                ...assistant?.model,
                provider,
                model,
              });
            }}
          >
            <option value="openai-gpt-4">OpenAI GPT-4</option>
            <option value="openai-gpt-3.5-turbo">OpenAI GPT-3.5 Turbo</option>
            <option value="anthropic-claude-3-sonnet">Anthropic Claude 3 Sonnet</option>
            <option value="anthropic-claude-3-haiku">Anthropic Claude 3 Haiku</option>
            <option value="google-gemini-pro">Google Gemini Pro</option>
            <option value="google-gemini-2.0-flash">Google Gemini 2.0 Flash</option>
          </select>
        </ConfigSection>

        {/* Temperature Slider */}
        <ConfigSection
          title="Temperature"
          description="Temperature is a parameter that controls the creativity or randomness of the responses generated by the LLM."
        >
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Conservative</span>
              <span className="text-gray-400">{((assistant?.model?.temperature || 0.7) * 100).toFixed(0)}%</span>
              <span>Creative</span>
            </div>
            <input
              type="range"
              min="0"
              max="100"
              value={(assistant?.model?.temperature || 0.7) * 100}
              onChange={(e) => {
                const temperature = parseInt(e.target.value) / 100;
                handleFieldChange('model', {
                  ...assistant?.model,
                  temperature,
                });
              }}
              className="w-full h-1 bg-[#0F0F0F] rounded-lg appearance-none cursor-pointer"
            />
          </div>
        </ConfigSection>

        {/* Token Limit */}
        <ConfigSection
          title="Limit token usage"
          description="Limit the maximum number of tokens that the LLM can predict. A limit will be applied if the value is greater than 0."
        >
          <input
            type="number"
            value={assistant?.model?.maxTokens || 500}
            onChange={(e) => {
              const maxTokens = parseInt(e.target.value) || 500;
              handleFieldChange('model', {
                ...assistant?.model,
                maxTokens,
              });
            }}
            className="w-24 bg-[#0F0F0F] p-2 rounded-lg border border-gray-800 text-right"
            min="1"
            max="4000"
          />
        </ConfigSection>

        {/* Knowledge Base */}
        <ConfigSection
          title="Knowledge base"
          description="Provide the LLM with domain-specific information to help it answer questions more accurately."
          action={
            <button
              onClick={() => setIsKnowledgeBaseModalOpen(true)}
              className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100"
            >
              Add document
            </button>
          }
        >
          <div
            className="flex items-center justify-between p-3 bg-[#0F0F0F] rounded-lg border border-gray-800 cursor-pointer hover:border-gray-700"
            onClick={() => {
              setSelectedDocument({
                name: "Roofing Knowledge Base.txt",
                content: `Overview of the Roofing Industry
Residential vs. Commercial Roofing: Residential roofs are typically steep-sloped (e.g. asphalt shingles) on houses; crews of 4-6 people can reroof an average home in a day. Commercial roofs are often large, flat or low-slope and use single-ply membranes (TPO/EPDM/PVC) or built-up systems; they require specialized equipment, more crew, and take days to months.

Common Customer Concerns: Customers often worry about cost and value (roofing is expensive), quality of materials and workmanship, and timeline/disruption of the project. Other frequent concerns include warranties and reliability ("What if it leaks?") and financing options.

Types of Roofing Jobs
- Roof Replacement: Complete tear-off of old roofing and installation of a new roof system.
- Roof Repair: Fixing specific problems (leaks, missing shingles, minor damage).
- Roof Inspection: Professional assessment of roof condition to determine needed work.
- Roof Maintenance: Routine upkeep to extend roof life.
- New Construction Roofing: Installing a roof on a new building.
- Emergency Roofing Services: Urgent repairs for storm or unexpected damage.

Roofing Materials Overview
- Asphalt Shingles: Most common U.S. residential roofing (80%+ market share)
- Metal Roofing: Durable, energy-efficient, long-lasting (40-70 years)
- Tile & Slate: Premium materials, very long-lasting but require specialized installation
- Flat Roof Materials: TPO, EPDM, PVC membranes for commercial applications`,
                id: "FY6gQ8MDZ492rsvTDinq",
                lastUpdated: "Apr 28",
                dependentAgents: ["Secretary/Gatekeeper", "Outbound"]
              });
              setIsKnowledgeBaseModalOpen(true);
            }}
          >
            <div className="flex items-center gap-3">
              <FileText size={18} className="text-gray-400" />
              <span className="text-sm">Roofing Knowledge Base.txt</span>
            </div>
            <button>
              <MoreVertical size={18} className="text-gray-400" />
            </button>
          </div>
        </ConfigSection>

        {/* Knowledge Base Modal */}
        <Modal
          isOpen={isKnowledgeBaseModalOpen}
          onClose={() => {
            setIsKnowledgeBaseModalOpen(false);
            setSelectedDocument(null);
          }}
          title={selectedDocument ? selectedDocument.name : "Add document"}
        >
          {selectedDocument ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-400">Document ID</span>
                    <div className="flex items-center gap-1 px-2 py-1 bg-[#0F0F0F] rounded text-xs text-gray-400">
                      {selectedDocument.id}
                      <button className="hover:text-white">
                        <Copy size={12} />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-400">Last updated</span>
                    <span className="text-sm">{selectedDocument.lastUpdated}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Dependent agents</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedDocument.dependentAgents.map((agent) => (
                    <span key={agent} className="px-2 py-1 bg-[#0F0F0F] rounded-full text-xs">
                      {agent}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">File Content</h3>
                <div className="bg-[#0F0F0F] rounded-lg p-4 text-sm whitespace-pre-wrap font-mono">
                  {selectedDocument.content}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 border-2 border-dashed border-gray-800 rounded-lg">
              <div className="mb-2">
                <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FileText size={24} className="text-gray-400" />
                </div>
                <p className="text-sm text-gray-400 mb-1">Click or drag files to upload</p>
                <p className="text-xs text-gray-600">Up to 21 MB each.</p>
              </div>
              <div className="flex justify-center gap-2 text-xs text-gray-600">
                <span className="px-2 py-1 bg-gray-800 rounded">txt</span>
                <span className="px-2 py-1 bg-gray-800 rounded">pdf</span>
                <span className="px-2 py-1 bg-gray-800 rounded">docx</span>
              </div>
            </div>
          )}
        </Modal>

        {/* Tools Section */}
        <ConfigSection
          title="Tools"
          description="Provide the agent with tools it can use to help users."
          action={
            <button className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100">
              Add tool
            </button>
          }
        >
          <div className="flex items-center justify-between p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
            <div className="flex items-center gap-3">
              <Tool size={18} className="text-gray-400" />
              <div>
                <p className="text-sm">end_call</p>
                <p className="text-xs text-gray-400">System</p>
              </div>
            </div>
            <button className="p-1.5 hover:bg-gray-800 rounded">
              <ChevronDown size={18} className="text-gray-400" />
            </button>
          </div>
        </ConfigSection>

        {/* Workspace Secrets */}
        <ConfigSection
          title="Workspace Secrets"
          description="Create and manage secrets that can be accessed across your workspace."
          action={
            <button className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100">
              Add secret
            </button>
          }
        >
          <div className="flex items-center justify-between p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
            <div className="flex items-center gap-3">
              <Key size={18} className="text-gray-400" />
              <span className="text-sm">No secrets added</span>
            </div>
          </div>
        </ConfigSection>

        {/* Test Assistant */}
        <ConfigSection
          title="Test Assistant"
          description="Test your assistant configuration with a sample message."
        >
          <div className="space-y-3">
            <button
              onClick={async () => {
                try {
                  const result = await testAssistant("Hello, this is a test message");
                  console.log('Test result:', result);
                  alert('Test completed successfully! Check the console for details.');
                } catch (err) {
                  console.error('Test failed:', err);
                  alert('Test failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
                }
              }}
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Testing...' : 'Test Assistant'}
            </button>
            <p className="text-xs text-gray-400">
              This will validate your assistant configuration and test basic functionality.
            </p>
          </div>
        </ConfigSection>
        </div>
      )}

      {activeTab === 'voice' && (
        <div className="max-w-[640px] mx-auto py-8 space-y-6">
          {/* Voice Selection */}
          <ConfigSection
            title="Voice"
            description="Select the Vapi voice you want to use for the agent."
          >
            <select
              className="w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700 text-sm"
              value={assistant?.voice?.voiceId || 'Lily'}
              onChange={(e) => {
                handleFieldChange('voice', {
                  ...assistant?.voice,
                  provider: 'vapi',
                  voiceId: e.target.value,
                });
              }}
            >
              <option value="Elliot">Elliot</option>
              <option value="Kylie">Kylie</option>
              <option value="Rohan">Rohan</option>
              <option value="Lily">Lily</option>
              <option value="Savannah">Savannah</option>
              <option value="Hana">Hana</option>
              <option value="Neha">Neha</option>
              <option value="Cole">Cole</option>
              <option value="Harry">Harry</option>
              <option value="Paige">Paige</option>
              <option value="Spencer">Spencer</option>
            </select>
          </ConfigSection>

          {/* Use Flash */}
          <ConfigSection
            title="Use Flash"
            description="Flash is our new recommended model for low latency use cases."
          >
            <div className="flex items-center justify-between">
              <div className="text-sm">
                <p className="mb-1">Your agent will use Flash v2.</p>
                <p className="text-gray-400">For more comparison between Turbo and Flash, <a href="#" className="text-white hover:underline">refer here</a>.</p>
              </div>
              <div className="relative inline-block w-12 h-6 rounded-full bg-green-500">
                <div className="absolute left-1 top-1 w-4 h-4 bg-white rounded-full"></div>
              </div>
            </div>
          </ConfigSection>

          {/* TTS Output Format */}
          <ConfigSection
            title="TTS output format"
            description="Select the output format you want to use for ElevenLabs text to speech."
          >
            <button className="flex items-center justify-between w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700">
              <span>PCM 16000 Hz</span>
              <ChevronDown size={18} className="text-gray-400" />
            </button>
          </ConfigSection>

          {/* Pronunciation Dictionaries */}
          <ConfigSection
            title="Pronunciation Dictionaries"
            description="Lexicon dictionary files will apply pronunciation replacements to agent responses."
            action={
              <button className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100">
                Add dictionary
              </button>
            }
          >
            <div className="text-sm text-gray-400">
              <p className="mb-2">Currently, the phoneme function of the pronunciation dictionaries only works with the Turbo v2 model, while the alias function works with all models.</p>
              <p className="text-xs">.pls .txt .xml Max 1.6 MB</p>
            </div>
          </ConfigSection>

          {/* Optimize Streaming Latency */}
          <ConfigSection
            title="Optimize streaming latency"
            description="Configure latency optimizations for the speech generation. Latency can be optimized at the cost of quality."
          >
            <input
              type="range"
              min="0"
              max="100"
              defaultValue="60"
              className="w-full h-1 bg-[#0F0F0F] rounded-lg appearance-none cursor-pointer"
            />
          </ConfigSection>

          {/* Stability */}
          <ConfigSection
            title="Stability"
            description="Higher values will make speech more consistent, but it can also make it sound monotone. Lower values will make speech sound more expressive, but may lead to instabilities."
          >
            <input
              type="range"
              min="0"
              max="100"
              defaultValue="70"
              className="w-full h-1 bg-[#0F0F0F] rounded-lg appearance-none cursor-pointer"
            />
          </ConfigSection>

          {/* Speed */}
          <ConfigSection
            title="Speed"
            description="Controls the speed of the generated speech. Values below 1.0 will slow down the speech, while values above 1.0 will speed it up. Extreme values may affect the quality of the generated speech."
          >
            <input
              type="range"
              min="0"
              max="100"
              defaultValue="50"
              className="w-full h-1 bg-[#0F0F0F] rounded-lg appearance-none cursor-pointer"
            />
          </ConfigSection>

          {/* Similarity */}
          <ConfigSection
            title="Similarity"
            description="Higher values will boost the overall clarity and consistency of the voice. Very high values may lead to artifacts. Adjusting this value to find the right balance is recommended."
          >
            <input
              type="range"
              min="0"
              max="100"
              defaultValue="85"
              className="w-full h-1 bg-[#0F0F0F] rounded-lg appearance-none cursor-pointer"
            />
          </ConfigSection>
        </div>
      )}

      {activeTab === 'analysis' && (
        <div className="max-w-[640px] mx-auto py-8 space-y-6">
          {/* Evaluation Criteria */}
          <ConfigSection
            title="Evaluation criteria"
            description="Define custom criteria to evaluate conversations against. You can find the evaluation results for each conversation in the history tab."
            action={
              <button className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100">
                Add criteria
              </button>
            }
          >
            <div className="flex items-center gap-2 p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
              <span className="text-sm text-gray-400">No evaluation criteria defined</span>
            </div>
          </ConfigSection>

          {/* Data Collection */}
          <ConfigSection
            title="Data collection"
            description="Define custom data specifications to extract from conversation transcripts. You can find the evaluation results for each conversation in the history tab."
            action={
              <button className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100">
                Add item
              </button>
            }
          >
            <div className="flex items-center gap-2 p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
              <span className="text-sm text-gray-400">No data collection items defined</span>
            </div>
          </ConfigSection>
        </div>
      )}

      {activeTab === 'security' && (
        <div className="max-w-[640px] mx-auto py-8 space-y-6">
          {/* Authentication */}
          <ConfigSection
            title="Enable authentication"
            description="Require users to authenticate before connecting to the agent."
          >
            <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
              <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
            </div>
          </ConfigSection>

          {/* Allowlist */}
          <ConfigSection
            title="Allowlist"
            description="Specify the hosts that will be allowed to connect to this agent."
            action={
              <button className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100">
                Add host
              </button>
            }
          >
            <div className="flex items-center gap-2 bg-[#0F0F0F] rounded-lg border border-gray-800 p-2">
              <div className="flex-1 flex items-center gap-2">
                <Globe2 size={18} className="text-gray-400" />
                <input
                  type="text"
                  placeholder="example.com"
                  className="flex-1 bg-transparent border-none outline-none text-sm placeholder-gray-600"
                />
              </div>
              <button className="p-1 hover:bg-gray-800 rounded">
                <Copy size={16} className="text-gray-400" />
              </button>
            </div>
          </ConfigSection>

          {/* Enable Overrides */}
          <ConfigSection
            title="Enable overrides"
            description="Choose which parts of the config can be overridden by the client at the start of the conversation."
          >
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Agent language</span>
                <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
                  <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">First message</span>
                <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
                  <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">System prompt</span>
                <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
                  <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Voice</span>
                <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
                  <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
                </div>
              </div>
            </div>
          </ConfigSection>

          {/* Webhook Settings */}
          <ConfigSection
            title="Fetch initiation client data from webhook"
            description="If enabled, the conversation initiation client data will be fetched from the webhook defined in the settings when receiving Twilio or SIP trunk calls."
          >
            <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
              <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
            </div>
          </ConfigSection>

          {/* Post-Call Webhook */}
          <ConfigSection
            title="Post-Call Webhook"
            description="Override the post-call webhook configured in settings for this agent."
            action={
              <button className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100">
                Create Webhook
              </button>
            }
          >
            <div className="flex items-center gap-2 p-3 bg-[#0F0F0F] rounded-lg border border-gray-800">
              <span className="text-sm text-gray-400">No webhook configured</span>
            </div>
          </ConfigSection>

          {/* Call Limits */}
          <ConfigSection
            title="Concurrent Calls Limit"
            description="The maximum number of concurrent calls allowed. Matching the subscription concurrency limit."
          >
            <input
              type="number"
              defaultValue="-1"
              className="w-24 bg-[#0F0F0F] p-2 rounded-lg border border-gray-800 text-right"
            />
          </ConfigSection>

          <ConfigSection
            title="Daily Calls Limit"
            description="The maximum number of calls allowed per day."
          >
            <input
              type="number"
              defaultValue="100000"
              className="w-24 bg-[#0F0F0F] p-2 rounded-lg border border-gray-800 text-right"
            />
          </ConfigSection>
        </div>
      )}

      {activeTab === 'advanced' && (
        <div className="max-w-[640px] mx-auto py-8 space-y-6">
          {/* Turn Timeout */}
          <ConfigSection
            title="Turn timeout"
            description="The maximum number of seconds since the user last spoke. If exceeded, the agent will respond and force a turn. A value of -1 means the agent will never timeout and always wait for a response from the user."
          >
            <input
              type="number"
              defaultValue="7"
              className="w-24 bg-[#0F0F0F] p-2 rounded-lg border border-gray-800 text-right"
            />
          </ConfigSection>

          {/* Silence and Call Timeout */}
          <ConfigSection
            title="Silence end call timeout"
            description="The maximum number of seconds since the user last spoke. If exceeded, the call will terminate. A value of -1 means there is no fixed cutoff."
          >
            <input
              type="number"
              defaultValue="20"
              className="w-24 bg-[#0F0F0F] p-2 rounded-lg border border-gray-800 text-right"
            />
          </ConfigSection>

          {/* Max Conversation Duration */}
          <ConfigSection
            title="Max conversation duration"
            description="The maximum number of seconds that a conversation can last."
          >
            <input
              type="number"
              defaultValue="300"
              className="w-24 bg-[#0F0F0F] p-2 rounded-lg border border-gray-800 text-right"
            />
          </ConfigSection>

          {/* Keywords */}
          <ConfigSection
            title="Keywords"
            description="Define a comma-separated list of keywords that have a higher likelihood of being predicted correctly."
          >
            <textarea
              className="w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700 min-h-[100px] text-sm"
              placeholder="Enter keywords..."
            />
          </ConfigSection>

          {/* User Input Audio Format */}
          <ConfigSection
            title="User input audio format"
            description="Select the audio format you want to use for automatic speech recognition."
          >
            <button className="flex items-center justify-between w-full bg-[#0F0F0F] p-3 rounded-lg border border-gray-800 hover:border-gray-700">
              <span>PCM 16000 Hz</span>
              <ChevronDown size={18} className="text-gray-400" />
            </button>
          </ConfigSection>

          {/* Client Events */}
          <ConfigSection
            title="Client Events"
            description="Select the events that should be sent to the client. If the 'audio' event is disabled, the agent will only provide text responses without TTS. If 'interruption' event is disabled, the agent will ignore user interruption and speak until the end of response."
          >
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1.5 bg-[#0F0F0F] rounded-full text-sm border border-gray-800 flex items-center gap-1">
                audio
                <button className="ml-1 hover:text-white">×</button>
              </span>
              <span className="px-3 py-1.5 bg-[#0F0F0F] rounded-full text-sm border border-gray-800 flex items-center gap-1">
                interruption
                <button className="ml-1 hover:text-white">×</button>
              </span>
              <span className="px-3 py-1.5 bg-[#0F0F0F] rounded-full text-sm border border-gray-800 flex items-center gap-1">
                user_transcript
                <button className="ml-1 hover:text-white">×</button>
              </span>
              <span className="px-3 py-1.5 bg-[#0F0F0F] rounded-full text-sm border border-gray-800 flex items-center gap-1">
                agent_response
                <button className="ml-1 hover:text-white">×</button>
              </span>
              <span className="px-3 py-1.5 bg-[#0F0F0F] rounded-full text-sm border border-gray-800 flex items-center gap-1">
                agent_response_correction
                <button className="ml-1 hover:text-white">×</button>
              </span>
            </div>
          </ConfigSection>

          {/* RAG Configuration */}
          <ConfigSection
            title="RAG Configuration"
            description="Fine-tune Retrieval-Augmented Generation (RAG) used by the Knowledge Base of this agent."
          >
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-4">Embedding model</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-[#0F0F0F] rounded-lg border border-gray-800">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">English optimized</span>
                      <div className="w-4 h-4 rounded-full border-2 border-white"></div>
                    </div>
                    <p className="text-xs text-gray-400">Effective English-focused</p>
                  </div>
                  <div className="p-4 bg-[#0F0F0F] rounded-lg border border-gray-800">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Multilingual optimized</span>
                      <div className="w-4 h-4 rounded-full border-2 border-gray-800"></div>
                    </div>
                    <p className="text-xs text-gray-400">Multilingual but large artifact</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Maximum text length of document chunks retrieved</h4>
                <input
                  type="number"
                  defaultValue="50000"
                  className="w-full bg-[#0F0F0F] p-2 rounded-lg border border-gray-800"
                />
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Maximum vector distance of retrieved chunks</h4>
                <input
                  type="range"
                  min="0"
                  max="100"
                  defaultValue="70"
                  className="w-full h-1 bg-[#0F0F0F] rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </ConfigSection>

          {/* Privacy Settings */}
          <ConfigSection
            title="Privacy Settings"
            description="This section allows you to configure the privacy settings for the agent."
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Store Call Audio</span>
                <div className="relative inline-block w-12 h-6 rounded-full bg-green-500">
                  <div className="absolute left-1 top-1 w-4 h-4 bg-white rounded-full"></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm">Conversations Retention Period</span>
                  <input
                    type="number"
                    defaultValue="730"
                    className="w-24 bg-[#0F0F0F] p-2 rounded-lg border border-gray-800 text-right"
                  />
                </div>
                <p className="text-xs text-gray-400">Set the number of days to keep conversations (-1 for unlimited)</p>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Delete Transcript and Derived Fields (PII)</span>
                <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
                  <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Delete Audio</span>
                <div className="relative inline-block w-12 h-6 rounded-full bg-[#0F0F0F] border border-gray-800">
                  <div className="absolute left-1 top-1 w-4 h-4 bg-gray-400 rounded-full"></div>
                </div>
              </div>
            </div>
          </ConfigSection>
        </div>
      )}
    </div>
  );
};

interface TabButtonProps {
  label: string;
  active?: boolean;
  onClick?: () => void;
}

const TabButton: React.FC<TabButtonProps> = ({ label, active, onClick }) => (
  <button
    onClick={onClick}
    className={`px-6 py-3 text-sm font-medium border-b-2 -mb-[1px] transition-colors ${
      active
        ? 'border-white text-white'
        : 'border-transparent text-gray-400 hover:text-gray-300'
    }`}
  >
    {label}
  </button>
);