import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { config } from '../api/config.js';

// Import route handlers
import callsRouter from './routes/calls.js';
import assistantsRouter from './routes/assistants.js';
import phoneNumbersRouter from './routes/phoneNumbers.js';
import templatesRouter from './routes/templates.js';
import analyticsRouter from './routes/analytics.js';
import webhooksRouter from './routes/webhooks.js';
import healthRouter from './routes/health.js';

/**
 * Express application setup with Vapi API wrapper
 */
export function createApp(): express.Application {
  const app = express();

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  }));

  // CORS configuration
  app.use(cors({
    origin: config.cors.origin,
    credentials: config.cors.credentials,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // Logging middleware
  if (config.server.nodeEnv === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined'));
  }

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Health check endpoint (before other routes)
  app.use('/api/health', healthRouter);

  // API routes
  app.use('/api/calls', callsRouter);
  app.use('/api/assistants', assistantsRouter);
  app.use('/api/phone-numbers', phoneNumbersRouter);
  app.use('/api/templates', templatesRouter);
  app.use('/api/analytics', analyticsRouter);
  app.use('/api/webhooks', webhooksRouter);

  // Root endpoint
  app.get('/', (req, res) => {
    res.json({
      message: 'Vapi API Wrapper Server',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/api/health',
        calls: '/api/calls',
        assistants: '/api/assistants',
        phoneNumbers: '/api/phone-numbers',
        templates: '/api/templates',
        analytics: '/api/analytics',
        webhooks: '/api/webhooks',
      },
    });
  });

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      error: 'Endpoint not found',
      message: `The endpoint ${req.method} ${req.originalUrl} does not exist`,
      timestamp: new Date().toISOString(),
    });
  });

  // Global error handler
  app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
    console.error('Global error handler:', err);

    // Don't leak error details in production
    const isDevelopment = config.server.nodeEnv === 'development';

    res.status(err.status || 500).json({
      success: false,
      error: isDevelopment ? err.message : 'Internal server error',
      ...(isDevelopment && { stack: err.stack }),
      timestamp: new Date().toISOString(),
    });
  });

  return app;
}

/**
 * Start the server
 */
export function startServer(): void {
  const app = createApp();
  const port = config.server.port;

  app.listen(port, () => {
    console.log(`🚀 Vapi API Wrapper Server running on port ${port}`);
    console.log(`📊 Environment: ${config.server.nodeEnv}`);
    console.log(`🔗 Health check: http://localhost:${port}/api/health`);
    console.log(`📚 API docs: http://localhost:${port}/`);
  });
}

export default createApp;
