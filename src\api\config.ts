import { VapiClient } from '@vapi-ai/server-sdk';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Vapi API Configuration
 */
export class VapiConfig {
  private static instance: VapiClient;

  /**
   * Get the singleton Vapi client instance
   */
  static getClient(): VapiClient {
    if (!this.instance) {
      const token = process.env.VAPI_API_TOKEN;
      
      if (!token) {
        throw new Error('VAPI_API_TOKEN environment variable is required');
      }

      this.instance = new VapiClient({
        token,
        // Optional: Configure default timeout and retries
        timeoutInSeconds: 30,
        maxRetries: 3,
      });
    }

    return this.instance;
  }

  /**
   * Create a new Vapi client with custom options
   */
  static createClient(options: {
    token?: string;
    timeoutInSeconds?: number;
    maxRetries?: number;
  } = {}): VapiClient {
    const token = options.token || process.env.VAPI_API_TOKEN;
    
    if (!token) {
      throw new Error('VAPI_API_TOKEN is required');
    }

    return new VapiClient({
      token,
      timeoutInSeconds: options.timeoutInSeconds || 30,
      maxRetries: options.maxRetries || 3,
    });
  }
}

/**
 * Environment configuration
 */
export const config = {
  vapi: {
    apiToken: process.env.VAPI_API_TOKEN,
    baseUrl: process.env.VAPI_BASE_URL || 'https://api.vapi.ai',
  },
  server: {
    port: parseInt(process.env.PORT || '3001'),
    nodeEnv: process.env.NODE_ENV || 'development',
  },
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
  },
} as const;

export default VapiConfig;
