// Frontend API client for Vapi integration

const API_BASE_URL = 'https://api.vapi.ai';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface Assistant {
  id: string;
  name: string;
  firstMessage?: string;
  model?: {
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    messages?: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }>;
  };
  voice?: {
    provider: string;
    voiceId: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AssistantTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  config: any;
  tags: string[];
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAssistantRequest {
  name: string;
  firstMessage?: string;
  model?: {
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    messages?: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }>;
  };
  voice?: {
    provider: string;
    voiceId: string;
  };
}

class ApiClient {
  private getApiKey(): string {
    // Try to get API key from environment variables or localStorage
    const apiKey = import.meta.env.VITE_VAPI_API_KEY ||
                   localStorage.getItem('vapi_api_key') ||
                   prompt('Please enter your Vapi API key:');

    if (!apiKey) {
      throw new Error('Vapi API key is required. Please set VITE_VAPI_API_KEY environment variable or store it in localStorage.');
    }

    return apiKey;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;
    const apiKey = this.getApiKey();

    console.log('Making request to:', url);
    console.log('Request options:', { ...options, headers: { ...options.headers, Authorization: 'Bearer [HIDDEN]' } });

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        ...options.headers,
      },
      ...options,
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('Response data:', data);

    if (!response.ok) {
      throw new Error(data.message || data.error || `HTTP error! status: ${response.status}`);
    }

    // Vapi API returns data directly, not wrapped in a success object
    return {
      success: true,
      data: data,
      timestamp: new Date().toISOString(),
    };
  }

  // Assistant methods
  async getAssistants(): Promise<ApiResponse<Assistant[]>> {
    return this.request<Assistant[]>('/assistant');
  }

  async getAssistant(id: string): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/assistant/${id}`);
  }

  async createAssistant(assistant: CreateAssistantRequest): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>('/assistant', {
      method: 'POST',
      body: JSON.stringify(assistant),
    });
  }

  async updateAssistant(id: string, updates: Partial<CreateAssistantRequest>): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/assistant/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  async deleteAssistant(id: string): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/assistant/${id}`, {
      method: 'DELETE',
    });
  }

  async cloneAssistant(id: string, newName?: string): Promise<ApiResponse<Assistant>> {
    // Vapi doesn't have a direct clone endpoint, so we'll get the assistant and create a new one
    const original = await this.getAssistant(id);
    if (!original.success || !original.data) {
      throw new Error('Failed to get original assistant');
    }

    const clonedAssistant = {
      ...original.data,
      name: newName || `${original.data.name} (Copy)`,
    };

    // Remove the id and timestamps for creation
    delete (clonedAssistant as any).id;
    delete (clonedAssistant as any).createdAt;
    delete (clonedAssistant as any).updatedAt;

    return this.createAssistant(clonedAssistant);
  }

  async getAssistantMetrics(id: string): Promise<ApiResponse<any>> {
    // This might not be available in Vapi API, return mock data for now
    return {
      success: true,
      data: {
        totalCalls: 0,
        averageDuration: 0,
        successRate: 100,
      },
      timestamp: new Date().toISOString(),
    };
  }

  async testAssistant(id: string, testMessage?: string): Promise<ApiResponse<any>> {
    // This might not be available in Vapi API, return mock data for now
    return {
      success: true,
      data: {
        message: 'Test completed successfully',
        response: 'Hello! This is a test response from the assistant.',
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Template methods (local templates since Vapi doesn't have a templates API)
  async getTemplates(): Promise<ApiResponse<AssistantTemplate[]>> {
    const templates: AssistantTemplate[] = [
      {
        id: 'customer-support',
        name: 'Customer Support Agent',
        description: 'A helpful customer support assistant',
        category: 'support',
        config: {
          name: 'Customer Support Assistant',
          firstMessage: 'Hello! How can I help you today?',
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 500,
            messages: [
              {
                role: 'system',
                content: 'You are a helpful customer support representative. Be polite, professional, and helpful in resolving customer issues.',
              },
            ],
          },
          voice: {
            provider: 'vapi',
            voiceId: 'jennifer',
          },
        },
        tags: ['support', 'customer-service'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'sales-agent',
        name: 'Sales Representative',
        description: 'A persuasive sales assistant',
        category: 'sales',
        config: {
          name: 'Sales Assistant',
          firstMessage: 'Hi! I\'d love to learn more about your needs and see how we can help.',
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.8,
            maxTokens: 600,
            messages: [
              {
                role: 'system',
                content: 'You are a skilled sales representative. Be friendly, persuasive, and focus on understanding customer needs to provide the best solutions.',
              },
            ],
          },
          voice: {
            provider: 'vapi',
            voiceId: 'jennifer',
          },
        },
        tags: ['sales', 'lead-generation'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'appointment-scheduler',
        name: 'Appointment Scheduler',
        description: 'An efficient scheduling assistant',
        category: 'scheduling',
        config: {
          name: 'Scheduling Assistant',
          firstMessage: 'Hello! I\'m here to help you schedule an appointment. What works best for you?',
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.6,
            maxTokens: 400,
            messages: [
              {
                role: 'system',
                content: 'You are an appointment scheduling assistant. Be efficient, clear about available times, and helpful in finding the best appointment slots.',
              },
            ],
          },
          voice: {
            provider: 'vapi',
            voiceId: 'jennifer',
          },
        },
        tags: ['scheduling', 'appointments'],
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    return {
      success: true,
      data: templates,
      timestamp: new Date().toISOString(),
    };
  }

  async getTemplate(id: string): Promise<ApiResponse<AssistantTemplate>> {
    const templates = await this.getTemplates();
    const template = templates.data?.find(t => t.id === id);

    if (!template) {
      throw new Error('Template not found');
    }

    return {
      success: true,
      data: template,
      timestamp: new Date().toISOString(),
    };
  }

  async createAssistantFromTemplate(
    templateId: string,
    customizations?: Partial<CreateAssistantRequest>
  ): Promise<ApiResponse<Assistant>> {
    const template = await this.getTemplate(templateId);
    if (!template.success || !template.data) {
      throw new Error('Template not found');
    }

    const assistantData = {
      ...template.data.config,
      ...customizations,
    };

    return this.createAssistant(assistantData);
  }

  async getTemplateCategories(): Promise<ApiResponse<string[]>> {
    return {
      success: true,
      data: ['support', 'sales', 'scheduling'],
      timestamp: new Date().toISOString(),
    };
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    try {
      // Test the connection by trying to get assistants
      await this.getAssistants();
      return {
        success: true,
        data: {
          status: 'healthy',
          message: 'Connected to Vapi API successfully',
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString(),
      };
    }
  }
}

export const apiClient = new ApiClient();
