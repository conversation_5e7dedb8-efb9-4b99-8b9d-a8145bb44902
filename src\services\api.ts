// Frontend API client for Vapi integration

const API_BASE_URL = 'http://localhost:3001/api';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface Assistant {
  id: string;
  name: string;
  firstMessage?: string;
  systemMessage?: string;
  model?: {
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
  };
  voice?: {
    provider: string;
    voiceId: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AssistantTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  config: any;
  tags: string[];
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAssistantRequest {
  name: string;
  firstMessage?: string;
  systemMessage?: string;
  model?: {
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
  };
  voice?: {
    provider: string;
    voiceId: string;
  };
}

class ApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  }

  // Assistant methods
  async getAssistants(): Promise<ApiResponse<Assistant[]>> {
    return this.request<Assistant[]>('/assistants');
  }

  async getAssistant(id: string): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/assistants/${id}`);
  }

  async createAssistant(assistant: CreateAssistantRequest): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>('/assistants', {
      method: 'POST',
      body: JSON.stringify(assistant),
    });
  }

  async updateAssistant(id: string, updates: Partial<CreateAssistantRequest>): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/assistants/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  async deleteAssistant(id: string): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/assistants/${id}`, {
      method: 'DELETE',
    });
  }

  async cloneAssistant(id: string, newName?: string): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/assistants/${id}/clone`, {
      method: 'POST',
      body: JSON.stringify({ name: newName }),
    });
  }

  async getAssistantMetrics(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/assistants/${id}/metrics`);
  }

  async testAssistant(id: string, testMessage?: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/assistants/${id}/test`, {
      method: 'POST',
      body: JSON.stringify({ testMessage }),
    });
  }

  // Template methods
  async getTemplates(): Promise<ApiResponse<AssistantTemplate[]>> {
    return this.request<AssistantTemplate[]>('/templates');
  }

  async getTemplate(id: string): Promise<ApiResponse<AssistantTemplate>> {
    return this.request<AssistantTemplate>(`/templates/${id}`);
  }

  async createAssistantFromTemplate(
    templateId: string, 
    customizations?: Partial<CreateAssistantRequest>
  ): Promise<ApiResponse<Assistant>> {
    return this.request<Assistant>(`/templates/${templateId}/create-assistant`, {
      method: 'POST',
      body: JSON.stringify(customizations || {}),
    });
  }

  async getTemplateCategories(): Promise<ApiResponse<string[]>> {
    return this.request<string[]>('/templates/categories');
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.request<any>('/health');
  }
}

export const apiClient = new ApiClient();
