import React, { useState } from 'react';
import { Phone, Plus, MoreHorizontal, Search } from 'lucide-react';
import { Modal } from '../components/Modal';

interface PhoneNumber {
  number: string;
  label: string;
  status: 'active' | 'inactive';
  lastUsed: string;
}

export const PhoneNumbers: React.FC = () => {
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold mb-1">Phone Numbers</h1>
            <p className="text-gray-400">Import and manage your phone numbers</p>
          </div>
          <button 
            onClick={() => setIsImportModalOpen(true)}
            className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
          >
            <Plus size={16} />
            Import number
          </button>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search phone numbers..."
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {phoneNumbers.length > 0 ? (
          /* Phone numbers table */
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800">
            <div className="grid grid-cols-[1fr,200px,200px,40px] px-4 py-3 border-b border-gray-800">
              <div className="text-sm text-gray-400">Number</div>
              <div className="text-sm text-gray-400">Status</div>
              <div className="text-sm text-gray-400">Last used</div>
              <div></div>
            </div>
            
            {phoneNumbers.map((number, index) => (
              <div
                key={index}
                className="grid grid-cols-[1fr,200px,200px,40px] px-4 py-3 hover:bg-[#252525] transition-colors"
              >
                <div className="text-sm font-medium flex items-center gap-2">
                  <Phone size={16} className="text-gray-400" />
                  {number.number}
                  {number.label && (
                    <span className="px-2 py-0.5 bg-gray-800 rounded-full text-xs text-gray-400">
                      {number.label}
                    </span>
                  )}
                </div>
                <div className="text-sm">
                  <span className={`px-2 py-0.5 rounded-full text-xs ${
                    number.status === 'active' 
                      ? 'bg-green-500/20 text-green-500' 
                      : 'bg-gray-500/20 text-gray-400'
                  }`}>
                    {number.status}
                  </span>
                </div>
                <div className="text-sm text-gray-400">{number.lastUsed}</div>
                <div className="flex justify-end">
                  <button className="p-1 hover:bg-[#333333] rounded-md transition-colors">
                    <MoreHorizontal size={16} className="text-gray-400" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Empty state */
          <div className="bg-[#1A1A1A] rounded-lg border border-gray-800 p-12 text-center">
            <div className="w-12 h-12 bg-[#252525] rounded-lg flex items-center justify-center mx-auto mb-4">
              <Phone size={24} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium mb-2">No phone numbers</h3>
            <p className="text-sm text-gray-400 mb-6">You don't have any phone numbers yet.</p>
            <button 
              onClick={() => setIsImportModalOpen(true)}
              className="px-4 py-2 bg-[#252525] text-white rounded-lg text-sm hover:bg-[#333333] transition-colors inline-flex items-center gap-2"
            >
              <Plus size={16} />
              Import number
            </button>
          </div>
        )}
      </div>

      {/* Import Modal */}
      <Modal 
        isOpen={isImportModalOpen} 
        onClose={() => setIsImportModalOpen(false)}
        title="Import phone number"
      >
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-medium mb-1">Import from provider</h3>
            <p className="text-sm text-gray-400 mb-4">Select a provider to import your phone numbers from.</p>
            <div className="grid grid-cols-2 gap-4">
              <button className="p-4 bg-[#0F0F0F] border border-gray-800 rounded-lg hover:border-gray-700 transition-colors text-left">
                <img src="https://images.pexels.com/photos/5474296/pexels-photo-5474296.jpeg" alt="Twilio" className="w-8 h-8 rounded mb-2" />
                <div className="font-medium mb-1">Twilio</div>
                <p className="text-sm text-gray-400">Import numbers from your Twilio account</p>
              </button>
              <button className="p-4 bg-[#0F0F0F] border border-gray-800 rounded-lg hover:border-gray-700 transition-colors text-left">
                <img src="https://images.pexels.com/photos/5474298/pexels-photo-5474298.jpeg" alt="Telnyx" className="w-8 h-8 rounded mb-2" />
                <div className="font-medium mb-1">Telnyx</div>
                <p className="text-sm text-gray-400">Import numbers from your Telnyx account</p>
              </button>
              <button className="p-4 bg-[#0F0F0F] border border-gray-800 rounded-lg hover:border-gray-700 transition-colors text-left">
                <img src="https://images.pexels.com/photos/5474297/pexels-photo-5474297.jpeg" alt="Vonage" className="w-8 h-8 rounded mb-2" />
                <div className="font-medium mb-1">Vonage</div>
                <p className="text-sm text-gray-400">Import numbers from your Vonage account</p>
              </button>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-1">Manual import</h3>
            <p className="text-sm text-gray-400 mb-4">Enter your phone number details manually.</p>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Phone number</label>
                <input
                  type="tel"
                  placeholder="+****************"
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Label (optional)</label>
                <input
                  type="text"
                  placeholder="e.g., Sales, Support"
                  className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
              Import number
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};