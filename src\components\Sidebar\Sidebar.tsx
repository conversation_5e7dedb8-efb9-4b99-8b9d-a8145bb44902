import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  PhoneCall,
  BookOpen,
  Phone,
  Settings,
  ArrowLeft,
  Headphones,
  Bell,
  PanelLeftClose,
  PanelLeftOpen,
  Wrench
} from 'lucide-react';
import { NavItem } from './NavItem';
import { UserProfile } from './UserProfile';

interface SidebarProps {
  currentPage: string;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ currentPage, isCollapsed, onToggleCollapse }) => {
  const navigate = useNavigate();

  return (
    <aside className={`${isCollapsed ? 'w-[72px]' : 'w-[220px]'} bg-[#1A1A1A] flex flex-col border-r border-gray-800 transition-all duration-300`}>
      <div className="p-4 border-b border-gray-800 flex items-center justify-between">
        <div className={`flex items-center ${isCollapsed ? 'justify-center w-full' : ''}`}>
          <div className="w-8 h-8 bg-white rounded-sm flex items-center justify-center text-black mr-2">
            {/* Logo placeholder */}
          </div>
          <div className={`${isCollapsed ? 'hidden' : 'block'}`}>
            <div className="text-xs font-bold">VoiceBox</div>
            <div className="text-sm font-medium">Conversational AI</div>
          </div>
        </div>
        <button
          onClick={onToggleCollapse}
          className={`p-1 hover:bg-gray-800 rounded-lg transition-colors ${isCollapsed ? 'absolute right-2' : ''}`}
        >
          {isCollapsed ? (
            <PanelLeftOpen size={18} className="text-gray-400" />
          ) : (
            <PanelLeftClose size={18} className="text-gray-400" />
          )}
        </button>
      </div>

      <div className="flex-1 overflow-y-auto py-4">
        <NavItem
          icon={<LayoutDashboard size={18} />}
          label="Dashboard"
          active={currentPage === 'dashboard'}
          onClick={() => navigate('/app/dashboard')}
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<Users size={18} />}
          label="Agents"
          active={currentPage === 'agents'}
          onClick={() => navigate('/app/agents')}
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<PhoneCall size={18} />}
          label="Call History"
          active={currentPage === 'call-history'}
          onClick={() => navigate('/app/call-history')}
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<BookOpen size={18} />}
          label="Knowledge Base"
          active={currentPage === 'knowledge-base'}
          onClick={() => navigate('/app/knowledge-base')}
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<Wrench size={18} />}
          label="Tools"
          active={currentPage === 'tools'}
          onClick={() => navigate('/app/tools')}
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<Phone size={18} />}
          label="Phone Numbers"
          active={currentPage === 'phone-numbers'}
          onClick={() => navigate('/app/phone-numbers')}
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<Settings size={18} />}
          label="Settings"
          active={currentPage === 'settings'}
          onClick={() => navigate('/app/settings')}
          isCollapsed={isCollapsed}
        />
      </div>

      <div className="border-t border-gray-800 pt-2">
        <NavItem
          icon={<ArrowLeft size={18} />}
          label="Back to VoiceBox"
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<Headphones size={18} />}
          label="Audio Tools"
          hasSubmenu
          isCollapsed={isCollapsed}
        />
        <NavItem
          icon={<Bell size={18} />}
          label="Notifications"
          isCollapsed={isCollapsed}
        />
        <UserProfile
          name="Max Hunter"
          workspace="My Workspace"
          avatarText="M"
          isCollapsed={isCollapsed}
        />
      </div>
    </aside>
  );
}