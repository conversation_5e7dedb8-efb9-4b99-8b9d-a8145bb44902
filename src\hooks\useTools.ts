import { useState, useEffect, useCallback } from 'react';
import { apiClient, Tool, CreateToolRequest } from '../services/api';

export const useTools = () => {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTools = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getTools();
      if (response.success && response.data) {
        // Sort tools by creation date (newest first)
        const sortedTools = response.data.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        setTools(sortedTools);
      } else {
        setError(response.error || 'Failed to fetch tools');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tools');
    } finally {
      setLoading(false);
    }
  }, []);

  const getTool = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getTool(id);
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get tool');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get tool';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createTool = useCallback(async (tool: CreateToolRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.createTool(tool);
      if (response.success && response.data) {
        setTools(prev => [response.data!, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create tool');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create tool';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateTool = useCallback(async (id: string, updates: Partial<CreateToolRequest>) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.updateTool(id, updates);
      if (response.success && response.data) {
        setTools(prev => 
          prev.map(tool => 
            tool.id === id ? response.data! : tool
          )
        );
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update tool');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update tool';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteTool = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.deleteTool(id);
      if (response.success) {
        setTools(prev => prev.filter(tool => tool.id !== id));
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete tool');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete tool';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Helper functions for filtering and categorizing
  const getToolsByType = useCallback((type: Tool['type']) => {
    return tools.filter(tool => tool.type === type);
  }, [tools]);

  const getBuiltInTools = useCallback(() => {
    return tools.filter(tool => 
      ['transferCall', 'endCall', 'dtmf', 'sms'].includes(tool.type)
    );
  }, [tools]);

  const getCustomTools = useCallback(() => {
    return tools.filter(tool => tool.type === 'function');
  }, [tools]);

  const getQueryTools = useCallback(() => {
    return tools.filter(tool => tool.type === 'query');
  }, [tools]);

  const getToolTypeIcon = useCallback((type: Tool['type']) => {
    switch (type) {
      case 'function':
        return '🔧';
      case 'transferCall':
        return '📞';
      case 'endCall':
        return '📴';
      case 'dtmf':
        return '🔢';
      case 'sms':
        return '💬';
      case 'query':
        return '🔍';
      default:
        return '⚙️';
    }
  }, []);

  const getToolTypeColor = useCallback((type: Tool['type']) => {
    switch (type) {
      case 'function':
        return 'bg-blue-500/20 text-blue-400';
      case 'transferCall':
        return 'bg-green-500/20 text-green-400';
      case 'endCall':
        return 'bg-red-500/20 text-red-400';
      case 'dtmf':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'sms':
        return 'bg-purple-500/20 text-purple-400';
      case 'query':
        return 'bg-orange-500/20 text-orange-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  }, []);

  const formatToolName = useCallback((tool: Tool) => {
    return tool.name || tool.function?.name || `${tool.type} Tool`;
  }, []);

  const formatToolDescription = useCallback((tool: Tool) => {
    return tool.function?.description || `A ${tool.type} tool for your assistant`;
  }, []);

  useEffect(() => {
    fetchTools();
  }, [fetchTools]);

  return {
    tools,
    loading,
    error,
    fetchTools,
    getTool,
    createTool,
    updateTool,
    deleteTool,
    getToolsByType,
    getBuiltInTools,
    getCustomTools,
    getQueryTools,
    getToolTypeIcon,
    getToolTypeColor,
    formatToolName,
    formatToolDescription,
    clearError: () => setError(null),
  };
};
