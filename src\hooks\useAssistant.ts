import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { apiClient, Assistant, CreateAssistantRequest } from '../services/api';

export const useAssistant = () => {
  const { agentId: id } = useParams<{ agentId: string }>();
  const [assistant, setAssistant] = useState<Assistant | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  const fetchAssistant = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      console.log('Fetching assistant with ID:', id);
      const response = await apiClient.getAssistant(id);
      console.log('Assistant response:', response);
      if (response.success && response.data) {
        setAssistant(response.data);
      } else {
        setError(response.error || 'Failed to fetch assistant');
      }
    } catch (err) {
      console.error('Error fetching assistant:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch assistant');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const updateAssistant = useCallback(async (updates: Partial<CreateAssistantRequest>) => {
    if (!id) return;

    try {
      setSaving(true);
      setError(null);
      const response = await apiClient.updateAssistant(id, updates);
      if (response.success && response.data) {
        setAssistant(response.data);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSaving(false);
    }
  }, [id]);

  const testAssistant = useCallback(async (testMessage?: string) => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.testAssistant(id, testMessage);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to test assistant');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to test assistant';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [id]);

  const getAssistantMetrics = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getAssistantMetrics(id);
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get assistant metrics');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get assistant metrics';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [id]);

  // Auto-save functionality
  const autoSave = useCallback(async (field: keyof CreateAssistantRequest, value: any) => {
    if (!assistant) return;

    try {
      await updateAssistant({ [field]: value });
    } catch (err) {
      console.error('Auto-save failed:', err);
    }
  }, [assistant, updateAssistant]);

  // Debounced auto-save for text fields
  const debouncedAutoSave = useCallback(
    debounce((field: keyof CreateAssistantRequest, value: any) => {
      autoSave(field, value);
    }, 1000),
    [autoSave]
  );

  useEffect(() => {
    fetchAssistant();
  }, [fetchAssistant]);

  return {
    assistant,
    loading,
    error,
    saving,
    updateAssistant,
    testAssistant,
    getAssistantMetrics,
    autoSave,
    debouncedAutoSave,
    clearError: () => setError(null),
  };
};

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
