import React from 'react';
import { Play, Pause, RotateCcw, Download, X, ChevronRight } from 'lucide-react';

interface CallDetailsProps {
  call: {
    id: string;
    date: string;
    time: string;
    agent: string;
    duration: string;
    status: 'successful' | 'error' | 'unknown';
    summary: string;
    metadata: {
      connectionDuration: string;
      cost: number;
      llmPricePreview: number;
    };
  };
  onClose: () => void;
}

export const CallDetails: React.FC<CallDetailsProps> = ({ call, onClose }) => {
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(0);
  const [activeTab, setActiveTab] = React.useState<'overview' | 'transcription' | 'client-data'>('overview');

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50">
      <div className="absolute inset-y-0 right-0 w-[480px] bg-[#1A1A1A] shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <div>
            <h2 className="text-sm font-medium mb-1">Conversation with {call.agent}</h2>
            <div className="text-xs text-gray-400">{call.id}</div>
          </div>
          <button 
            onClick={onClose}
            className="p-1 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-400" />
          </button>
        </div>

        {/* Audio Player */}
        <div className="p-4 border-b border-gray-800">
          <div className="flex items-center gap-4 mb-4">
            <button 
              onClick={togglePlayback}
              className="w-10 h-10 rounded-full bg-white text-black flex items-center justify-center hover:bg-gray-100 transition-colors"
            >
              {isPlaying ? <Pause size={20} /> : <Play size={20} />}
            </button>
            <div className="flex-1">
              <div className="h-1 bg-gray-800 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-white rounded-full"
                  style={{ width: `${(currentTime / 143) * 100}%` }}
                />
              </div>
              <div className="flex justify-between mt-1 text-xs text-gray-400">
                <span>0:00</span>
                <span>2:43</span>
              </div>
            </div>
            <button className="p-2 hover:bg-gray-800 rounded-lg transition-colors">
              <RotateCcw size={20} className="text-gray-400" />
            </button>
            <button className="p-2 hover:bg-gray-800 rounded-lg transition-colors">
              <Download size={20} className="text-gray-400" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-800">
          <button
            onClick={() => setActiveTab('overview')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'overview' 
                ? 'border-white text-white' 
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('transcription')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'transcription' 
                ? 'border-white text-white' 
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Transcription
          </button>
          <button
            onClick={() => setActiveTab('client-data')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'client-data' 
                ? 'border-white text-white' 
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
          >
            Client data
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="p-4 space-y-6">
              <div>
                <h3 className="text-sm font-medium mb-2">Summary</h3>
                <p className="text-sm text-gray-400">{call.summary}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Call status</h3>
                <span className={`inline-flex px-2 py-1 rounded-full text-xs ${
                  call.status === 'successful' ? 'bg-green-500/20 text-green-500' :
                  call.status === 'error' ? 'bg-red-500/20 text-red-500' :
                  'bg-gray-500/20 text-gray-400'
                }`}>
                  {call.status.charAt(0).toUpperCase() + call.status.slice(1)}
                </span>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Metadata</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Date</span>
                    <span>{call.date}, {call.time}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Connection duration</span>
                    <span>{call.metadata.connectionDuration}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Cost (credits)</span>
                    <div>
                      <span>{call.metadata.cost}</span>
                      <span className="text-xs text-gray-600 ml-1">Development discount applied</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">LLM Price Preview</span>
                    <span>${call.metadata.llmPricePreview.toFixed(4)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'transcription' && (
            <div className="p-4">
              <div className="space-y-4">
                <div className="flex gap-3">
                  <div className="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center text-sm">H</div>
                  <div className="flex-1">
                    <div className="text-sm mb-1">Harper</div>
                    <div className="text-sm text-gray-400">Hi there... this is Harper from Atlas Roofing... I see you just filled out our form online... So I'm just reaching out to help you schedule a time to meet with one of our roofing specialists...</div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center text-sm">U</div>
                  <div className="flex-1">
                    <div className="text-sm mb-1">User</div>
                    <div className="text-sm text-gray-400">Yes, I did. We had some hail damage recently and I'd like to get it checked out.</div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center text-sm">H</div>
                  <div className="flex-1">
                    <div className="text-sm mb-1">Harper</div>
                    <div className="text-sm text-gray-400">I understand you're concerned about potential hail damage. We can definitely help with that. Would you be available this Tuesday at 2 PM for an inspection?</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'client-data' && (
            <div className="p-4">
              <div className="bg-[#0F0F0F] rounded-lg border border-gray-800 divide-y divide-gray-800">
                <div className="p-3">
                  <div className="text-sm text-gray-400 mb-1">Name</div>
                  <div className="text-sm">John Smith</div>
                </div>
                <div className="p-3">
                  <div className="text-sm text-gray-400 mb-1">Phone</div>
                  <div className="text-sm">+****************</div>
                </div>
                <div className="p-3">
                  <div className="text-sm text-gray-400 mb-1">Email</div>
                  <div className="text-sm"><EMAIL></div>
                </div>
                <div className="p-3">
                  <div className="text-sm text-gray-400 mb-1">Address</div>
                  <div className="text-sm">123 Main St, Anytown, USA 12345</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};