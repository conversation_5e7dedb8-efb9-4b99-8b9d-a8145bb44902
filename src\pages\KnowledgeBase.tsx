import React, { useState } from 'react';
import { Globe2, FileText, Type as TypeIcon, Plus, MoreHorizontal, Search } from 'lucide-react';
import { Modal } from '../components/Modal';
import { useModal } from '../context/ModalContext';

interface KnowledgeBaseItem {
  name: string;
  size: string;
  createdBy: string;
  lastUpdated: string;
}

const knowledgeBaseItems: KnowledgeBaseItem[] = [
  {
    name: 'Roofing Knowledge Base.txt',
    size: '20.1 kB',
    createdBy: 'Max Hunter',
    lastUpdated: 'Apr 28, 2025, 7:09 PM',
  },
];

export const KnowledgeBase: React.FC = () => {
  const { 
    isUrlModalOpen, 
    isFilesModalOpen, 
    isTextModalOpen, 
    openUrlModal, 
    openFilesModal, 
    openTextModal, 
    closeModals 
  } = useModal();

  const [url, setUrl] = useState('');
  const [textName, setTextName] = useState('');
  const [textContent, setTextContent] = useState('');

  const handleUrlSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle URL submission
    closeModals();
  };

  const handleTextSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle text submission
    closeModals();
  };

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold mb-1">Knowledge Base</h1>
            <p className="text-gray-400">Manage your AI agent's knowledge</p>
          </div>
          <div className="flex gap-3">
            <button 
              onClick={openUrlModal}
              className="px-4 py-2 bg-[#1A1A1A] text-white rounded-lg text-sm hover:bg-[#252525] transition-colors flex items-center gap-2"
            >
              <Globe2 size={16} />
              Add URL
            </button>
            <button 
              onClick={openFilesModal}
              className="px-4 py-2 bg-[#1A1A1A] text-white rounded-lg text-sm hover:bg-[#252525] transition-colors flex items-center gap-2"
            >
              <FileText size={16} />
              Add Files
            </button>
            <button 
              onClick={openTextModal}
              className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
            >
              <TypeIcon size={16} />
              Create Text
            </button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-3 mb-6">
          <div className="flex-1 relative">
            <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search Knowledge Base..."
              className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
            />
          </div>
          <button className="px-3 py-2 bg-[#1A1A1A] border border-gray-800 rounded-lg text-sm hover:border-gray-700 transition-colors flex items-center gap-2">
            <Plus size={16} />
            Type
          </button>
        </div>

        {/* Table */}
        <div className="bg-[#1A1A1A] rounded-lg border border-gray-800">
          <div className="grid grid-cols-[2fr,100px,200px,200px,40px] px-4 py-3 border-b border-gray-800">
            <div className="text-sm text-gray-400">Name</div>
            <div className="text-sm text-gray-400">Size</div>
            <div className="text-sm text-gray-400">Created by</div>
            <div className="text-sm text-gray-400">Last updated</div>
            <div></div>
          </div>
          
          {knowledgeBaseItems.map((item, index) => (
            <div
              key={index}
              className="grid grid-cols-[2fr,100px,200px,200px,40px] px-4 py-3 hover:bg-[#252525] transition-colors cursor-pointer"
            >
              <div className="text-sm font-medium flex items-center gap-2">
                <FileText size={16} className="text-gray-400" />
                {item.name}
              </div>
              <div className="text-sm text-gray-400">{item.size}</div>
              <div className="text-sm text-gray-400">{item.createdBy}</div>
              <div className="text-sm text-gray-400">{item.lastUpdated}</div>
              <div className="flex justify-end">
                <button className="p-1 hover:bg-[#333333] rounded-md transition-colors">
                  <MoreHorizontal size={16} className="text-gray-400" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add URL Modal */}
      <Modal isOpen={isUrlModalOpen} onClose={closeModals} title="Add URL">
        <form onSubmit={handleUrlSubmit}>
          <div className="mb-4">
            <label htmlFor="url" className="block text-sm font-medium text-gray-400 mb-1">
              URL
            </label>
            <input
              type="url"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
          </div>
          <div className="flex justify-end">
            <button
              type="submit"
              className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors"
            >
              Add URL
            </button>
          </div>
        </form>
      </Modal>

      {/* Add Files Modal */}
      <Modal isOpen={isFilesModalOpen} onClose={closeModals} title="Add Files">
        <div className="text-center py-12 border-2 border-dashed border-gray-800 rounded-lg">
          <div className="mb-2">
            <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText size={24} className="text-gray-400" />
            </div>
            <p className="text-sm text-gray-400 mb-1">Click or drag files to upload</p>
            <p className="text-xs text-gray-600">Up to 21 MB each.</p>
          </div>
          <div className="flex justify-center gap-2 text-xs text-gray-600">
            <span className="px-2 py-1 bg-gray-800 rounded">epub</span>
            <span className="px-2 py-1 bg-gray-800 rounded">pdf</span>
            <span className="px-2 py-1 bg-gray-800 rounded">docx</span>
            <span className="px-2 py-1 bg-gray-800 rounded">txt</span>
            <span className="px-2 py-1 bg-gray-800 rounded">html</span>
          </div>
        </div>
      </Modal>

      {/* Create Text Modal */}
      <Modal isOpen={isTextModalOpen} onClose={closeModals} title="Create Text">
        <form onSubmit={handleTextSubmit}>
          <div className="mb-4">
            <label htmlFor="textName" className="block text-sm font-medium text-gray-400 mb-1">
              Text Name
            </label>
            <input
              type="text"
              id="textName"
              value={textName}
              onChange={(e) => setTextName(e.target.value)}
              placeholder="Enter a name for your text"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="textContent" className="block text-sm font-medium text-gray-400 mb-1">
              Text Content
            </label>
            <textarea
              id="textContent"
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              placeholder="Enter your text content here"
              className="w-full h-48 bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors resize-none"
              required
            />
          </div>
          <div className="flex justify-end">
            <button
              type="submit"
              className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors"
            >
              Create Text
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
};