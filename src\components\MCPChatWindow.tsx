import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader, Minimize2, Maximize2, X } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

interface MCPChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
  isMinimized: boolean;
}

export const MCPChatWindow: React.FC<MCPChatWindowProps> = ({
  isOpen,
  onClose,
  onMinimize,
  isMinimized,
}) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'agent',
      content: `Hello! I'm your LangGraph MCP agent with access to powerful tools! 🤖

I can help you with:

🔧 **Code Modifications**
- Create new files and components
- Edit existing code
- Update specific functions

📞 **Vapi Integrations**
- Create tools (Google Calendar, Slack, GHL, etc.)
- Manage assistants and knowledge bases
- Handle call operations

🎨 **UI Components**
- Create React components
- Update styling with Tailwind
- Modify pages and layouts

**Try asking me:**
- "Create a new modal component for user settings"
- "Add a Google Calendar tool to the Tools page"
- "List all Vapi assistants"
- "Create a knowledge base with product docs"

What would you like me to help you with?`,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && !isMinimized) {
      inputRef.current?.focus();
    }
  }, [isOpen, isMinimized]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Simulate MCP agent response
      // In a real implementation, this would call the LangGraph agent
      const agentResponse = await simulateMCPAgentResponse(inputValue);

      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: agentResponse,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, agentMessage]);
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Simulate MCP agent response
  const simulateMCPAgentResponse = async (input: string): Promise<string> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('create') && lowerInput.includes('component')) {
      return `I'll help you create a new component! I can see you want to create a component. Let me use the ui_component tool to set this up for you.

🔧 **Using UI Component Tool:**
- Component Type: component
- Framework: React + TypeScript
- Styling: Tailwind CSS

What specific component would you like me to create? For example:
- A new modal component
- A form component
- A data display component
- A navigation component

Please provide more details about the component's functionality and I'll generate the code for you!`;
    }

    if (lowerInput.includes('vapi') || lowerInput.includes('tool')) {
      return `I can help you with Vapi integrations! I have access to the vapi_integration tool which can:

🔧 **Vapi Services Available:**
- **Tools**: Create, update, delete Vapi tools (Google Calendar, Slack, GHL, etc.)
- **Assistants**: Manage AI assistants and their configurations
- **Knowledge Base**: Upload files and create knowledge bases
- **Calls**: Manage call operations and history

What specific Vapi integration would you like me to help you with? For example:
- "Create a Google Calendar tool"
- "Set up a new assistant with knowledge base"
- "Add a Slack notification tool"
- "Update an existing tool configuration"`;
    }

    if (lowerInput.includes('modify') || lowerInput.includes('edit') || lowerInput.includes('change')) {
      return `I can help you modify code! I have access to the modify_code tool which can:

🔧 **Code Modification Capabilities:**
- **Create**: New files and components
- **Edit**: Existing code files
- **Update**: Specific functions or sections
- **View**: Current file contents

What would you like me to modify? For example:
- "Update the Tools page to add a new feature"
- "Create a new API endpoint"
- "Modify the sidebar navigation"
- "Add a new React hook"

Please be specific about what changes you'd like me to make!`;
    }

    if (lowerInput.includes('help') || lowerInput.includes('what can you do')) {
      return `I'm your LangGraph MCP agent with access to powerful tools! Here's what I can help you with:

🤖 **My Capabilities:**

**1. Code Modifications** 🔧
- Create new files and components
- Edit existing code
- Update specific functions
- View file contents

**2. Vapi Integrations** 📞
- Create and manage tools (Google Calendar, Slack, GHL, etc.)
- Configure AI assistants
- Manage knowledge bases
- Handle call operations

**3. UI Components** 🎨
- Create React components
- Update styling with Tailwind
- Modify pages and layouts
- Add new features to the interface

**Example requests:**
- "Create a new modal component for user settings"
- "Add a Google Calendar tool to the Tools page"
- "Update the sidebar to include a new navigation item"
- "Create a knowledge base with uploaded files"

What would you like me to help you with today?`;
    }

    // Default response
    return `I understand you want to: "${input}"

I have access to these MCP tools to help you:
- **modify_code**: For creating/editing files
- **vapi_integration**: For Vapi services
- **ui_component**: For React UI updates

Could you provide more specific details about what you'd like me to do? For example:
- Which files need to be modified?
- What specific functionality should be added?
- Are there any particular requirements or constraints?

The more details you provide, the better I can assist you!`;
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed bottom-4 right-4 bg-[#1A1A1A] border border-gray-800 rounded-lg shadow-2xl z-50 transition-all duration-300 ${
      isMinimized ? 'w-80 h-12' : 'w-96 h-[600px]'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-800">
        <div className="flex items-center gap-2">
          <Bot size={18} className="text-blue-400" />
          <span className="text-sm font-medium text-white">MCP Agent</span>
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={onMinimize}
            className="p-1 hover:bg-gray-800 rounded transition-colors"
            title={isMinimized ? "Maximize" : "Minimize"}
          >
            {isMinimized ? (
              <Maximize2 size={14} className="text-gray-400" />
            ) : (
              <Minimize2 size={14} className="text-gray-400" />
            )}
          </button>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-800 rounded transition-colors"
            title="Close"
          >
            <X size={14} className="text-gray-400" />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 h-[480px]">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.type === 'agent' && (
                  <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot size={16} className="text-blue-400" />
                  </div>
                )}
                <div
                  className={`max-w-[80%] p-3 rounded-lg text-sm ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-[#0F0F0F] text-gray-200 border border-gray-800'
                  }`}
                >
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  <div className="text-xs opacity-60 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
                {message.type === 'user' && (
                  <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <User size={16} className="text-gray-300" />
                  </div>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <Bot size={16} className="text-blue-400" />
                </div>
                <div className="bg-[#0F0F0F] border border-gray-800 p-3 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Loader size={16} className="text-blue-400 animate-spin" />
                    <span className="text-sm text-gray-400">Agent is thinking...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-800">
            <div className="flex gap-2">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me to modify code, create tools, or update UI..."
                className="flex-1 bg-[#0F0F0F] border border-gray-800 rounded-lg px-3 py-2 text-sm text-white placeholder-gray-500 focus:outline-none focus:border-gray-700 transition-colors"
                disabled={isLoading}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Send size={16} />
              </button>
            </div>
            <div className="text-xs text-gray-500 mt-2">
              💡 Try: "Create a new component", "Add a Vapi tool", "Modify the sidebar"
            </div>
          </div>
        </>
      )}
    </div>
  );
};
