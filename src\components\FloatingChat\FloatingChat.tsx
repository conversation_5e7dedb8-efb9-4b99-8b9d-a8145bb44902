import React, { useState, useRef, useEffect } from 'react';
import { MessageSquare, X, Mic, Send } from 'lucide-react';
import { Visualization } from '../Visualization';

interface Position {
  x: number;
  y: number;
}

export const FloatingChat: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState<Position>({ x: window.innerWidth - 420, y: window.innerHeight - 600 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState<Position>({ x: 0, y: 0 });
  const [isListening, setIsListening] = useState(false);
  const [messages, setMessages] = useState<Array<{ type: 'user' | 'agent'; content: string }>>([]);
  const [inputValue, setInputValue] = useState('');
  
  const chatRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (chatRef.current) {
      const rect = chatRef.current.getBoundingClientRect();
      setIsDragging(true);
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newX = Math.min(Math.max(0, e.clientX - dragOffset.x), window.innerWidth - (chatRef.current?.offsetWidth || 0));
      const newY = Math.min(Math.max(0, e.clientY - dragOffset.y), window.innerHeight - (chatRef.current?.offsetHeight || 0));
      
      setPosition({ x: newX, y: newY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      setMessages([...messages, { type: 'user', content: inputValue }]);
      setInputValue('');
      
      // Simulate agent response
      setTimeout(() => {
        setMessages(prev => [...prev, { 
          type: 'agent', 
          content: "I'm here to help! How can I assist you today?" 
        }]);
      }, 1000);
    }
  };

  const toggleListening = () => {
    setIsListening(!isListening);
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-purple-600 rounded-full flex items-center justify-center shadow-lg hover:bg-purple-700 transition-colors"
        style={{ zIndex: 50 }}
      >
        <MessageSquare className="text-white" size={24} />
      </button>
    );
  }

  return (
    <div
      ref={chatRef}
      className="fixed bg-[#1A1A1A] w-[380px] rounded-lg shadow-xl border border-gray-800 flex flex-col"
      style={{
        left: position.x,
        top: position.y,
        height: '580px',
        zIndex: 50
      }}
    >
      {/* Header */}
      <div 
        className="p-4 border-b border-gray-800 flex items-center justify-between cursor-move"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center text-white">
            H
          </div>
          <div>
            <div className="font-medium">Harper</div>
            <div className="text-xs text-gray-400">Atlas Roofing</div>
          </div>
        </div>
        <button 
          onClick={() => setIsOpen(false)}
          className="p-1 hover:bg-gray-800 rounded-lg transition-colors"
        >
          <X size={20} className="text-gray-400" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-800 text-white'
              }`}
            >
              {message.content}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {isListening && (
        <div className="p-4 border-t border-gray-800">
          <Visualization isListening={isListening} />
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-gray-800">
        <div className="flex items-center gap-2">
          <button
            onClick={toggleListening}
            className={`p-2 rounded-lg transition-colors ${
              isListening 
                ? 'bg-red-500 hover:bg-red-600' 
                : 'hover:bg-gray-800'
            }`}
          >
            <Mic size={20} className={isListening ? 'text-white' : 'text-gray-400'} />
          </button>
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Type a message..."
            className="flex-1 bg-gray-800 rounded-lg px-3 py-2 text-sm placeholder-gray-500 focus:outline-none"
          />
          <button
            onClick={handleSendMessage}
            className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <Send size={20} className="text-gray-400" />
          </button>
        </div>
      </div>
    </div>
  );
};