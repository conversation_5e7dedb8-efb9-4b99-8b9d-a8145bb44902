import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Plus, MoreHorizontal, Bot, Headphones, Users, CircleDollarSign } from 'lucide-react';
import { Modal } from '../components/Modal';

interface Agent {
  id: string;
  name: string;
  createdBy: string;
  createdAt: string;
}

const agents: Agent[] = [
  { id: 'ghfa2B4hPfaMRwU8q8hg', name: '<PERSON>', createdBy: '<PERSON>', createdAt: 'Apr 27, 2025, 7:50 PM' },
  { id: 'kj2B4hPfaMRwU8q8hgL', name: '<PERSON>', createdBy: '<PERSON>', createdAt: 'Apr 27, 2025, 4:31 PM' },
  { id: 'mn9B4hPfaMRwU8q8hgX', name: 'Outbound', createdBy: '<PERSON>', createdAt: 'Apr 27, 2025, 3:45 PM' },
  { id: 'op4B4hPfaMRwU8q8hgY', name: '<PERSON>', createdBy: '<PERSON>', createdAt: 'Apr 24, 2025, 5:08 PM' },
  { id: 'qr7B4hPfaMRwU8q8hgZ', name: 'Secretary/Gatekeeper', createdBy: '<PERSON>', createdAt: 'Apr 24, 2025, 2:59 AM' },
  { id: 'st1B4hPfaMRwU8q8hgW', name: 'Tom - Atlas Construction', createdBy: 'Max Hunter', createdAt: 'Apr 23, 2025, 11:44 PM' },
  { id: 'uv5B4hPfaMRwU8q8hgV', name: 'John - Atlas Construction', createdBy: 'Max <PERSON>', createdAt: 'Apr 23, 2025, 11:34 PM' },
];

export const Agents: React.FC = () => {
  const navigate = useNavigate();
  const [isNewAgentModalOpen, setIsNewAgentModalOpen] = React.useState(false);
  const [agentName, setAgentName] = React.useState('');

  const handleCreateAgent = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle agent creation
    setIsNewAgentModalOpen(false);
    setAgentName('');
  };

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold mb-1">Agents</h1>
            <p className="text-gray-400">Create and manage your AI agents</p>
          </div>
          <div className="flex gap-3">
            <button className="px-3 py-1.5 bg-[#1A1A1A] text-white rounded-lg text-sm hover:bg-[#252525] transition-colors">
              Playground
            </button>
            <button 
              onClick={() => setIsNewAgentModalOpen(true)}
              className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
            >
              <Plus size={16} />
              New agent
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search agents..."
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {/* Table */}
        <div className="bg-[#1A1A1A] rounded-lg border border-gray-800">
          <div className="grid grid-cols-[1fr,200px,200px,40px] px-4 py-3 border-b border-gray-800">
            <div className="text-sm text-gray-400">Name</div>
            <div className="text-sm text-gray-400">Created by</div>
            <div className="text-sm text-gray-400">Created at</div>
            <div></div>
          </div>
          
          {agents.map((agent) => (
            <div
              key={agent.id}
              className="grid grid-cols-[1fr,200px,200px,40px] px-4 py-3 hover:bg-[#252525] transition-colors cursor-pointer"
              onClick={() => navigate(`/app/agents/${agent.id}`)}
            >
              <div className="text-sm font-medium">{agent.name}</div>
              <div className="text-sm text-gray-400">{agent.createdBy}</div>
              <div className="text-sm text-gray-400">{agent.createdAt}</div>
              <div className="flex justify-end">
                <button 
                  className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle menu click
                  }}
                >
                  <MoreHorizontal size={16} className="text-gray-400" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* New Agent Modal */}
      <Modal 
        isOpen={isNewAgentModalOpen} 
        onClose={() => setIsNewAgentModalOpen(false)}
        title="Create an AI agent"
      >
        <form onSubmit={handleCreateAgent}>
          <div className="mb-4">
            <label htmlFor="agentName" className="block text-sm font-medium text-gray-400 mb-1">
              AI Agent name
            </label>
            <input
              type="text"
              id="agentName"
              value={agentName}
              onChange={(e) => setAgentName(e.target.value)}
              placeholder="Customer support agent"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Blank template */}
            <div className="p-6 bg-[#0F0F0F] border border-gray-800 rounded-lg hover:border-gray-700 cursor-pointer transition-colors">
              <div className="flex items-center gap-2 mb-2">
                <Bot size={18} className="text-gray-400" />
                <span className="font-medium">Blank template</span>
              </div>
              <p className="text-sm text-gray-400 mb-4">Start with a blank template and customize your agent to suit your needs.</p>
              <div className="mt-3 flex items-center gap-1">
                <div className="w-6 h-6 rounded-full bg-purple-600 flex items-center justify-center text-xs">A</div>
                <span className="text-sm text-gray-400">Any</span>
              </div>
            </div>

            {/* Support agent */}
            <div className="p-6 bg-[#0F0F0F] border border-gray-800 rounded-lg hover:border-gray-700 cursor-pointer transition-colors">
              <div className="flex items-center gap-2 mb-2">
                <Headphones size={18} className="text-gray-400" />
                <span className="font-medium">Support agent</span>
              </div>
              <p className="text-sm text-gray-400 mb-4">Talk to Alexis, a dedicated support agent who is always ready to resolve any issues.</p>
              <div className="mt-3 flex items-center gap-1">
                <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center text-xs">A</div>
                <span className="text-sm text-gray-400">Alexis</span>
              </div>
            </div>

            {/* Mindfulness coach */}
            <div className="p-6 bg-[#0F0F0F] border border-gray-800 rounded-lg hover:border-gray-700 cursor-pointer transition-colors">
              <div className="flex items-center gap-2 mb-2">
                <Users size={18} className="text-gray-400" />
                <span className="font-medium">Mindfulness coach</span>
              </div>
              <p className="text-sm text-gray-400 mb-4">Speak with Joe, a mindfulness coach who helps you find calm and clarity.</p>
              <div className="mt-3 flex items-center gap-1">
                <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center text-xs">J</div>
                <span className="text-sm text-gray-400">Joe</span>
              </div>
            </div>

            {/* Sales agent */}
            <div className="p-6 bg-[#0F0F0F] border border-gray-800 rounded-lg hover:border-gray-700 cursor-pointer transition-colors">
              <div className="flex items-center gap-2 mb-2">
                <CircleDollarSign size={18} className="text-gray-400" />
                <span className="font-medium">Sales agent</span>
              </div>
              <p className="text-sm text-gray-400 mb-4">Talk to Harper, a sales agent who showcases how VoiceBox can transform your business.</p>
              <div className="mt-3 flex items-center gap-1">
                <div className="w-6 h-6 rounded-full bg-red-600 flex items-center justify-center text-xs">H</div>
                <span className="text-sm text-gray-400">Harper</span>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <button
              type="submit"
              className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors"
            >
              Create agent
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
};