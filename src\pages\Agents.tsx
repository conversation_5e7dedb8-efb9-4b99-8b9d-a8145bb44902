import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Plus, MoreHorizontal, Bot, Headphones, Users, CircleDollarSign, Trash2, Copy, AlertCircle } from 'lucide-react';
import { Modal } from '../components/Modal';
import { ApiKeySetup } from '../components/ApiKeySetup';
import { useAssistants } from '../hooks/useAssistants';
import { Assistant, AssistantTemplate } from '../services/api';

export const Agents: React.FC = () => {
  const navigate = useNavigate();
  const [isNewAgentModalOpen, setIsNewAgentModalOpen] = React.useState(false);
  const [agentName, setAgentName] = React.useState('');
  const [selectedTemplate, setSelectedTemplate] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = React.useState<string | null>(null);
  const [hasApiKey, setHasApiKey] = React.useState(false);

  // Check for API key on mount
  React.useEffect(() => {
    const apiKey = import.meta.env.VITE_VAPI_API_KEY || localStorage.getItem('vapi_api_key');
    setHasApiKey(!!apiKey);
  }, []);

  const {
    assistants,
    templates,
    loading,
    error,
    createAssistant,
    createAssistantFromTemplate,
    deleteAssistant,
    cloneAssistant,
    clearError,
  } = useAssistants();

  const handleCreateAgent = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!agentName.trim()) return;

    try {
      let newAssistant: Assistant;

      if (selectedTemplate) {
        // Create from template
        newAssistant = await createAssistantFromTemplate(selectedTemplate, {
          name: agentName,
        });
      } else {
        // Create blank assistant
        newAssistant = await createAssistant({
          name: agentName,
          firstMessage: "Hello! How can I help you today?",
          systemMessage: "You are a helpful AI assistant. Be polite, professional, and helpful.",
          model: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 500,
          },
          voice: {
            provider: 'elevenlabs',
            voiceId: 'default',
          },
        });
      }

      setIsNewAgentModalOpen(false);
      setAgentName('');
      setSelectedTemplate(null);

      // Navigate to the new agent's config page
      navigate(`/app/agents/${newAssistant.id}`);
    } catch (err) {
      console.error('Failed to create agent:', err);
    }
  };

  const handleDeleteAgent = async (id: string) => {
    try {
      await deleteAssistant(id);
      setShowDeleteConfirm(null);
    } catch (err) {
      console.error('Failed to delete agent:', err);
    }
  };

  const handleCloneAgent = async (id: string, originalName: string) => {
    try {
      await cloneAssistant(id, `${originalName} (Copy)`);
    } catch (err) {
      console.error('Failed to clone agent:', err);
    }
  };

  // Filter assistants based on search query
  const filteredAssistants = assistants.filter(assistant =>
    assistant.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  // Show API key setup if no key is found
  if (!hasApiKey) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#0F0F0F] text-white p-6">
        <ApiKeySetup onApiKeySet={() => setHasApiKey(true)} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-[#0F0F0F] text-white p-6">
      <div className="max-w-[1200px] mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold mb-1">Agents</h1>
            <p className="text-gray-400">Create and manage your AI agents</p>
          </div>
          <div className="flex gap-3">
            <button className="px-3 py-1.5 bg-[#1A1A1A] text-white rounded-lg text-sm hover:bg-[#252525] transition-colors">
              Playground
            </button>
            <button
              onClick={() => setIsNewAgentModalOpen(true)}
              className="px-3 py-1.5 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center gap-2"
            >
              <Plus size={16} />
              New agent
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
            <AlertCircle size={18} className="text-red-400" />
            <span className="text-red-400">{error}</span>
            <button
              onClick={clearError}
              className="ml-auto text-red-400 hover:text-red-300"
            >
              ×
            </button>
          </div>
        )}

        {/* Search */}
        <div className="relative mb-6">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search agents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-[#1A1A1A] border border-gray-800 rounded-lg py-2 pl-10 pr-4 text-sm placeholder-gray-400 focus:outline-none focus:border-gray-700 transition-colors"
          />
        </div>

        {/* Table */}
        <div className="bg-[#1A1A1A] rounded-lg border border-gray-800">
          <div className="grid grid-cols-[1fr,200px,200px,80px] px-4 py-3 border-b border-gray-800">
            <div className="text-sm text-gray-400">Name</div>
            <div className="text-sm text-gray-400">Model</div>
            <div className="text-sm text-gray-400">Created at</div>
            <div className="text-sm text-gray-400">Actions</div>
          </div>

          {loading && assistants.length === 0 ? (
            <div className="px-4 py-8 text-center text-gray-400">
              <div className="animate-spin w-6 h-6 border-2 border-gray-600 border-t-white rounded-full mx-auto mb-2"></div>
              Loading agents...
            </div>
          ) : filteredAssistants.length === 0 ? (
            <div className="px-4 py-8 text-center text-gray-400">
              {searchQuery ? 'No agents found matching your search.' : 'No agents created yet.'}
            </div>
          ) : (
            filteredAssistants.map((assistant) => (
              <div
                key={assistant.id}
                className="grid grid-cols-[1fr,200px,200px,80px] px-4 py-3 hover:bg-[#252525] transition-colors cursor-pointer"
                onClick={() => navigate(`/app/agents/${assistant.id}`)}
              >
                <div className="text-sm font-medium">{assistant.name}</div>
                <div className="text-sm text-gray-400">
                  {assistant.model?.model || 'Not configured'}
                </div>
                <div className="text-sm text-gray-400">
                  {formatDate(assistant.createdAt)}
                </div>
                <div className="flex justify-end gap-1">
                  <button
                    className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCloneAgent(assistant.id, assistant.name);
                    }}
                    title="Clone agent"
                  >
                    <Copy size={14} className="text-gray-400" />
                  </button>
                  <button
                    className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowDeleteConfirm(assistant.id);
                    }}
                    title="Delete agent"
                  >
                    <Trash2 size={14} className="text-red-400" />
                  </button>
                  <button
                    className="p-1 hover:bg-[#333333] rounded-md transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle more options
                    }}
                  >
                    <MoreHorizontal size={14} className="text-gray-400" />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* New Agent Modal */}
      <Modal
        isOpen={isNewAgentModalOpen}
        onClose={() => setIsNewAgentModalOpen(false)}
        title="Create an AI agent"
      >
        <form onSubmit={handleCreateAgent}>
          <div className="mb-4">
            <label htmlFor="agentName" className="block text-sm font-medium text-gray-400 mb-1">
              AI Agent name
            </label>
            <input
              type="text"
              id="agentName"
              value={agentName}
              onChange={(e) => setAgentName(e.target.value)}
              placeholder="Customer support agent"
              className="w-full bg-[#0F0F0F] border border-gray-800 rounded-lg py-2 px-3 text-sm placeholder-gray-600 focus:outline-none focus:border-gray-700 transition-colors"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Blank template */}
            <div
              className={`p-6 bg-[#0F0F0F] border rounded-lg cursor-pointer transition-colors ${
                selectedTemplate === null
                  ? 'border-white bg-[#1A1A1A]'
                  : 'border-gray-800 hover:border-gray-700'
              }`}
              onClick={() => setSelectedTemplate(null)}
            >
              <div className="flex items-center gap-2 mb-2">
                <Bot size={18} className="text-gray-400" />
                <span className="font-medium">Blank template</span>
              </div>
              <p className="text-sm text-gray-400 mb-4">Start with a blank template and customize your agent to suit your needs.</p>
              <div className="mt-3 flex items-center gap-1">
                <div className="w-6 h-6 rounded-full bg-purple-600 flex items-center justify-center text-xs">A</div>
                <span className="text-sm text-gray-400">Any</span>
              </div>
            </div>

            {/* Dynamic templates from API */}
            {templates.slice(0, 3).map((template) => {
              const getTemplateIcon = (category: string) => {
                switch (category) {
                  case 'support': return <Headphones size={18} className="text-gray-400" />;
                  case 'sales': return <CircleDollarSign size={18} className="text-gray-400" />;
                  case 'scheduling': return <Users size={18} className="text-gray-400" />;
                  default: return <Bot size={18} className="text-gray-400" />;
                }
              };

              const getTemplateColor = (category: string) => {
                switch (category) {
                  case 'support': return 'bg-blue-600';
                  case 'sales': return 'bg-red-600';
                  case 'scheduling': return 'bg-green-600';
                  default: return 'bg-purple-600';
                }
              };

              return (
                <div
                  key={template.id}
                  className={`p-6 bg-[#0F0F0F] border rounded-lg cursor-pointer transition-colors ${
                    selectedTemplate === template.id
                      ? 'border-white bg-[#1A1A1A]'
                      : 'border-gray-800 hover:border-gray-700'
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {getTemplateIcon(template.category)}
                    <span className="font-medium">{template.name}</span>
                  </div>
                  <p className="text-sm text-gray-400 mb-4">{template.description}</p>
                  <div className="mt-3 flex items-center gap-1">
                    <div className={`w-6 h-6 rounded-full ${getTemplateColor(template.category)} flex items-center justify-center text-xs`}>
                      {template.name.charAt(0)}
                    </div>
                    <span className="text-sm text-gray-400">{template.category}</span>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <button
              type="button"
              onClick={() => {
                setIsNewAgentModalOpen(false);
                setAgentName('');
                setSelectedTemplate(null);
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-white text-black rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Creating...' : 'Create agent'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(null)}
        title="Delete Agent"
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Are you sure you want to delete this agent? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-3">
            <button
              onClick={() => setShowDeleteConfirm(null)}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => showDeleteConfirm && handleDeleteAgent(showDeleteConfirm)}
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};